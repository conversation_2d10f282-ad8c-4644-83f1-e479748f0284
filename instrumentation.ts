/**
 * Next.js Instrumentation File
 * Registers Sentry server-side and edge runtime configurations
 */

import * as Sentry from '@sentry/nextjs';

/**
 * Register function called by Next.js to initialize instrumentation
 * This runs when the Next.js server starts up
 */
export async function register() {
  // Only initialize Sentry if DSN is configured and feature is enabled
  if (
    process.env.NEXT_PUBLIC_SENTRY_DSN &&
    process.env.FEATURE_SENTRY !== 'false'
  ) {
    // Initialize server-side Sentry for Node.js runtime
    if (process.env.NEXT_RUNTIME === 'nodejs') {
      await import('./sentry.server.config');
    }

    // Initialize edge runtime Sentry
    if (process.env.NEXT_RUNTIME === 'edge') {
      await import('./sentry.edge.config');
    }
  }
}

/**
 * Optional: Handle request errors for React Server Components
 * Requires @sentry/nextjs version 8.28.0 or higher and Next.js 15
 * 
 * This captures errors from nested React Server Components that
 * wouldn't normally be caught by the global error boundary
 */
export const onRequestError = Sentry.captureRequestError;
