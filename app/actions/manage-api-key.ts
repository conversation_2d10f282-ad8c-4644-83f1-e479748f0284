'use server';

import { Err, Ok, Result } from '@/lib/types/result';

// Simplified API key management without iron-session for now
// In production, you should use proper session management

export async function setApiKey(): Promise<Result<void>> {
  try {
    // For now, just use environment variables
    // In production, implement proper session management
    return Ok();
  } catch (error) {
    console.error('API key management failed:', error);
    return Err('Failed to set API key');
  }
}

export async function getApiKey(): Promise<Result<string | null>> {
  try {
    // For now, return null to use environment variables
    // In production, implement proper session management
    return Ok(null);
  } catch (error) {
    console.error('Failed to retrieve API key:', error);
    return Err('Failed to retrieve API key');
  }
}
