/**
 * Global Error Boundary for Next.js App Router
 * Captures React render errors and sends them to Sentry
 */

'use client';

import { useEffect } from 'react';
import NextError from 'next/error';
import * as Sentry from '@sentry/nextjs';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Global error boundary component
 * This catches all unhandled errors in the App Router
 */
export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    // Capture the error in Sentry
    Sentry.captureException(error, {
      tags: {
        component: 'global-error-boundary',
        errorBoundary: true,
      },
      extra: {
        digest: error.digest,
        componentStack: error.stack,
      },
      contexts: {
        errorBoundary: {
          componentStack: error.stack,
          errorInfo: {
            componentStack: error.stack,
          },
        },
      },
    });

    // Log error for development
    if (process.env.NODE_ENV === 'development') {
      console.error('Global Error Boundary caught an error:', error);
    }
  }, [error]);

  const handleReset = () => {
    // Add breadcrumb for user action
    Sentry.addBreadcrumb({
      category: 'user',
      message: 'User clicked reset button in global error boundary',
      level: 'info',
    });

    reset();
  };

  const handleGoHome = () => {
    // Add breadcrumb for user action
    Sentry.addBreadcrumb({
      category: 'navigation',
      message: 'User navigated to home from global error boundary',
      level: 'info',
    });

    window.location.href = '/';
  };

  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="max-w-md w-full mx-auto p-6">
            <div className="text-center space-y-6">
              {/* Error Icon */}
              <div className="flex justify-center">
                <div className="rounded-full bg-destructive/10 p-3">
                  <AlertTriangle className="h-8 w-8 text-destructive" />
                </div>
              </div>

              {/* Error Message */}
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-foreground">
                  Something went wrong
                </h1>
                <p className="text-muted-foreground">
                  We apologize for the inconvenience. An unexpected error has occurred.
                </p>
              </div>

              {/* Development Error Details */}
              {isDevelopment && (
                <div className="bg-muted p-4 rounded-lg text-left">
                  <h3 className="font-semibold text-sm mb-2">Error Details (Development)</h3>
                  <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                    {error.message}
                    {error.digest && `\nDigest: ${error.digest}`}
                  </pre>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={handleReset}
                  variant="default"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
                <Button
                  onClick={handleGoHome}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>
              </div>

              {/* Error ID for Support */}
              {Sentry.lastEventId() && (
                <div className="text-xs text-muted-foreground">
                  Error ID: {Sentry.lastEventId()}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Fallback to Next.js default error page if our custom UI fails */}
        <noscript>
          <NextError statusCode={500} />
        </noscript>
      </body>
    </html>
  );
}
