/**
 * Sentry Edge Configuration
 * Edge runtime Sentry initialization for Next.js App Router
 */

import * as Sentry from '@sentry/nextjs';
import { getSentryEnvironment, getSentryRelease } from '@/lib/sentry';

// Initialize Sentry for edge runtime operations
Sentry.init({
  // Data Source Name (DSN) - tells Sentry where to send events
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Environment (development, staging, production)
  environment: getSentryEnvironment(),

  // Release version for tracking deployments
  release: getSentryRelease(),

  // Performance Monitoring
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
  // We recommend adjusting this value in production
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '1.0'),

  // Edge runtime specific integrations
  integrations: [
    // HTTP integration for tracking outgoing requests
    Sentry.httpIntegration({
      tracing: {
        // Capture outgoing HTTP requests
        shouldCreateSpanForRequest: (url) => {
          // Don't trace Sentry requests to avoid infinite loops
          return !url.includes('sentry.io');
        },
      },
    }),

    // Request data integration
    Sentry.requestDataIntegration(),
  ],

  // Debug mode (only in development)
  debug: process.env.SENTRY_DEBUG === 'true' || process.env.NODE_ENV === 'development',

  // Send default PII (Personally Identifiable Information)
  // Set to true to include user IP, cookies, etc.
  sendDefaultPii: process.env.SENTRY_SEND_DEFAULT_PII === 'true',

  // Maximum breadcrumbs to keep (lower for edge runtime)
  maxBreadcrumbs: 50,

  // Attach stack traces to pure capture message calls
  attachStacktrace: true,

  // Before send hook - filter or modify events before sending
  beforeSend(event, hint) {
    // Don't send events in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out specific errors
    if (event.exception) {
      const error = hint.originalException;
      
      // Filter out network errors that are not actionable
      if (error instanceof Error) {
        if (
          error.message.includes('Network request failed') ||
          error.message.includes('fetch is not defined') ||
          error.message.includes('AbortError')
        ) {
          return null;
        }
      }
    }

    // Add edge-specific context
    event.contexts = {
      ...event.contexts,
      runtime: {
        name: 'edge',
        version: 'unknown',
      },
    };

    return event;
  },

  // Before send transaction hook
  beforeSendTransaction(event) {
    // Don't send transactions in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out health check transactions
    if (event.transaction?.includes('/api/health')) {
      return null;
    }

    return event;
  },

  // Error filtering
  ignoreErrors: [
    // Browser extensions
    'Non-Error promise rejection captured',
    'Script error.',
    
    // Network errors
    'NetworkError',
    'Failed to fetch',
    'Load failed',
    
    // Cancelled requests
    'AbortError',
    'The operation was aborted',
    
    // Edge runtime specific errors
    'Dynamic require of',
    'Cannot resolve module',
  ],

  // Ignore specific URLs
  denyUrls: [
    // Browser extensions
    /extensions\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
    
    // Ad blockers
    /adnxs\.com/i,
    /adsystem\.google\.com/i,
  ],

  // Initial scope configuration
  initialScope: {
    tags: {
      component: 'edge',
      runtime: 'edge',
    },
  },
});

// Export Sentry for edge runtime usage
export { Sentry };

// Log initialization in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Sentry edge runtime initialized:', {
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN ? '***configured***' : 'missing',
    environment: getSentryEnvironment(),
    release: getSentryRelease(),
    debug: process.env.SENTRY_DEBUG === 'true',
  });
}
