# 🚀 Next.js + Shadcn Bolt Web Base

A modern, full-stack template built with [Next.js](https://nextjs.org/) and [Shadcn UI](https://ui.shadcn.dev/), pre-configured to integrate with the following services:

* [RevenueCat](https://www.revenuecat.com/)
* [Nodely](https://nodely.io/)
* [ElevenLabs](https://elevenlabs.io/)
* [Pica](https://picagroup.com/)
* [Sentry](https://sentry.io/)
* [Lingo](https://lingo.dev/)
* [Dappier](https://dappier.com/)
* [Tavus](https://tavus.io/)([picagroup.com]

---

## 🧰 Integrated Services

### 1. [RevenueCat](https://www.revenuecat.com/)

Manage in-app subscriptions and purchases effortlessly.([Entri][3])

* **Integration**: Use RevenueCat's SDK to handle subscriptions.
* **Documentation**: [RevenueCat Docs](https://www.revenuecat.com/docs/)([RevenueCat][4])

* **Documentation**: [Entri API Reference](https://developers.entri.com/api-reference)([entri.com][5], [Entri][6])

### 3. [Nodely](https://nodely.io/)

Access Algorand and IPFS APIs with high reliability.

* **Integration**: Connect to Nodely's endpoints for blockchain interactions.
* **Documentation**: [Nodely Docs](https://nodely.io/docs/)([Nodely][7], [Nodely][8])

### 4. [ElevenLabs](https://elevenlabs.io/)

Implement AI-generated voice features in your app.([ElevenLabs][9])

* **Integration**: Use ElevenLabs' API for text-to-speech functionalities.
* **Documentation**: [ElevenLabs Docs](https://elevenlabs.io/docs/overview)([ElevenLabs][10], [ElevenLabs][9])

### 5. [Pica](https://picagroup.com/)

Enhance your app with AI-driven features and integrations.

* **Integration**: Leverage Pica's tools to build and deploy AI agents.
* **Documentation**: [Pica Docs](https://picagroup.com/faq-documentation)([GitHub][11], [picagroup.com][1])

### 6. [Sentry](https://sentry.io/)

Monitor and debug your application in real-time.

* **Integration**: Integrate Sentry for error tracking and performance monitoring.
* **Documentation**: [Sentry Docs](https://docs.sentry.io/)([docs.madcapsoftware.com][12])

### 7. [Lingo](https://lingo.dev/)

Automate localization for your web and mobile applications.([lingo.dev][14])

* **Integration**: Implement Lingo's AI-powered translation services.
* **Documentation**: [Lingo Docs](https://docs.lingo.dev/)([GitHub][15])

### 8. [Dappier](https://dappier.com/)

Enhance your app with AI search and custom copilots.

* **Integration**: Incorporate Dappier's APIs for advanced AI features.
* **Documentation**: [Dappier Docs](https://dappier.com/docs)

### 9. [21st.dev](https://21st.dev/)

Generate UI components using AI tools.

* **Integration**: Use 21st.dev's Magic AI for UI generation.
* **Documentation**: [21st.dev Docs](https://21st.dev/docs)([ElevenLabs][16])

### 10. [River](https://rvr.to/)

Host and manage community events seamlessly.([entri.com][5])

* **Integration**: Leverage River's platform for event management.
* **Documentation**: [River Docs](https://rvr.to/docs)([Nodely][17])

### 11. [DEV++](https://dev.to/++)

Access exclusive features and offers from top tech companies.

* **Integration**: Utilize DEV++ membership benefits in your development workflow.
* **Documentation**: [DEV++ Info](https://dev.to/++)

### 12. [Tavus](https://tavus.io/)

Create personalized video content at scale.

* **Integration**: Use Tavus' platform for generating conversational videos.
* **Documentation**: [Tavus Docs](https://tavus.io/docs)([Sentry Help Center][18])

---

## 🛠️ Getting Started

1. **Clone the Repository**

   ```bash
   git clone https://github.com/yourusername/nextjs-shadcn-bolt-starter.git
   cd nextjs-shadcn-bolt-starter
   ```

2. **Install Dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Configure Environment Variables**

   Create a `.env.local` file and add the necessary environment variables for the integrated services.

4. **Run the Development Server**

   ```bash
   npm run dev
   # or
   yarn dev
   ```

   Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

---

## 📄 License

This project is licensed under the [MIT License](LICENSE).

---
