/**
 * Sentry Test Page
 * Test page for verifying Sentry integration functionality
 */

'use client';

import { useState } from 'react';
import { useSentry, useSentryAPIError, useSentryPerformance } from '@/hooks/useSentry';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  Bug, 
  MessageSquare, 
  User, 
  Activity, 
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';

export default function SentryTestPage() {
  const {
    captureError,
    captureMessage,
    setUser,
    clearUser,
    addBreadcrumb,
    captureReplay,
    showUserFeedback,
    isEnabled,
    lastEventId,
  } = useSentry({
    enablePerformanceMonitoring: true,
    enableSessionReplay: true,
    enableUserFeedback: true,
    defaultTags: {
      page: 'sentry-test',
      component: 'test-page',
    },
  });

  const captureAPIError = useSentryAPIError();
  const measurePerformance = useSentryPerformance();

  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'pending'>>({});
  const [userEmail, setUserEmail] = useState('<EMAIL>');
  const [userId, setUserId] = useState('test-user-123');
  const [customMessage, setCustomMessage] = useState('Custom test message');
  const [customError, setCustomError] = useState('Custom test error');

  const updateTestResult = (test: string, result: 'success' | 'error' | 'pending') => {
    setTestResults(prev => ({ ...prev, [test]: result }));
  };

  // Test Functions
  const testCaptureError = () => {
    updateTestResult('error', 'pending');
    try {
      const error = new Error(customError);
      const eventId = captureError(error, {
        tags: {
          test: 'manual-error',
          source: 'test-page',
        },
        extra: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        },
      });
      
      if (eventId) {
        updateTestResult('error', 'success');
      } else {
        updateTestResult('error', 'error');
      }
    } catch (err) {
      updateTestResult('error', 'error');
    }
  };

  const testCaptureMessage = () => {
    updateTestResult('message', 'pending');
    try {
      const eventId = captureMessage(customMessage, 'info', {
        tags: {
          test: 'manual-message',
          source: 'test-page',
        },
        extra: {
          timestamp: new Date().toISOString(),
        },
      });
      
      if (eventId) {
        updateTestResult('message', 'success');
      } else {
        updateTestResult('message', 'error');
      }
    } catch (err) {
      updateTestResult('message', 'error');
    }
  };

  const testUnhandledError = () => {
    updateTestResult('unhandled', 'pending');
    // This will be caught by the global error boundary
    setTimeout(() => {
      throw new Error('Unhandled test error from setTimeout');
    }, 100);
    
    setTimeout(() => {
      updateTestResult('unhandled', 'success');
    }, 500);
  };

  const testSetUser = () => {
    updateTestResult('user', 'pending');
    try {
      setUser({
        id: userId,
        email: userEmail,
        username: `user_${userId}`,
        subscription: {
          plan: 'test',
          status: 'active',
        },
      });
      
      addBreadcrumb({
        category: 'auth',
        message: `Test user set: ${userEmail}`,
        level: 'info',
        data: { userId, email: userEmail },
      });
      
      updateTestResult('user', 'success');
    } catch (err) {
      updateTestResult('user', 'error');
    }
  };

  const testClearUser = () => {
    updateTestResult('clearUser', 'pending');
    try {
      clearUser();
      
      addBreadcrumb({
        category: 'auth',
        message: 'Test user cleared',
        level: 'info',
      });
      
      updateTestResult('clearUser', 'success');
    } catch (err) {
      updateTestResult('clearUser', 'error');
    }
  };

  const testAPIError = async () => {
    updateTestResult('api', 'pending');
    try {
      // Simulate API error
      const error = new Error('Test API Error');
      captureAPIError(
        error,
        '/api/test',
        'POST',
        500,
        'req-test-123',
        1500
      );
      
      updateTestResult('api', 'success');
    } catch (err) {
      updateTestResult('api', 'error');
    }
  };

  const testPerformance = async () => {
    updateTestResult('performance', 'pending');
    try {
      await measurePerformance(
        'function',
        'Test Performance Operation',
        async () => {
          // Simulate some work
          await new Promise(resolve => setTimeout(resolve, 1000));
          return 'test result';
        },
        {
          test: 'performance',
          operation: 'simulation',
        }
      );
      
      updateTestResult('performance', 'success');
    } catch (err) {
      updateTestResult('performance', 'error');
    }
  };

  const testSessionReplay = () => {
    updateTestResult('replay', 'pending');
    try {
      captureReplay();
      updateTestResult('replay', 'success');
    } catch (err) {
      updateTestResult('replay', 'error');
    }
  };

  const testUserFeedback = () => {
    updateTestResult('feedback', 'pending');
    try {
      showUserFeedback();
      updateTestResult('feedback', 'success');
    } catch (err) {
      updateTestResult('feedback', 'error');
    }
  };

  const testServerAPI = async () => {
    updateTestResult('server', 'pending');
    try {
      const response = await fetch('/api/sentry?action=test');
      const data = await response.json();
      
      if (response.ok && data.success) {
        updateTestResult('server', 'success');
      } else {
        updateTestResult('server', 'error');
      }
    } catch (err) {
      updateTestResult('server', 'error');
    }
  };

  const getStatusIcon = (status: 'success' | 'error' | 'pending' | undefined) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: 'success' | 'error' | 'pending' | undefined) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">Not Run</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Sentry Integration Test</h1>
          <p className="text-muted-foreground">
            Test all Sentry features and verify the integration is working correctly
          </p>
          
          {/* Status */}
          <div className="flex items-center justify-center gap-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span className="text-sm">
                Sentry Status: {isEnabled ? (
                  <Badge variant="default" className="bg-green-500">Enabled</Badge>
                ) : (
                  <Badge variant="destructive">Disabled</Badge>
                )}
              </span>
            </div>
            
            {lastEventId && (
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <span className="text-sm">
                  Last Event: <code className="text-xs bg-muted px-1 rounded">{lastEventId}</code>
                </span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Test Configuration
            </CardTitle>
            <CardDescription>
              Configure test parameters before running tests
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  placeholder="test-user-123"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="userEmail">User Email</Label>
                <Input
                  id="userEmail"
                  type="email"
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="customMessage">Custom Message</Label>
              <Input
                id="customMessage"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                placeholder="Custom test message"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="customError">Custom Error</Label>
              <Input
                id="customError"
                value={customError}
                onChange={(e) => setCustomError(e.target.value)}
                placeholder="Custom test error"
              />
            </div>
          </CardContent>
        </Card>

        {/* Error Monitoring Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-5 w-5" />
              Error Monitoring Tests
            </CardTitle>
            <CardDescription>
              Test error capture and reporting functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.error)}
                  <span className="text-sm">Capture Error</span>
                </div>
                <Button size="sm" onClick={testCaptureError}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.unhandled)}
                  <span className="text-sm">Unhandled Error</span>
                </div>
                <Button size="sm" onClick={testUnhandledError} variant="destructive">
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.api)}
                  <span className="text-sm">API Error</span>
                </div>
                <Button size="sm" onClick={testAPIError}>
                  Test
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Message and Context Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Message and Context Tests
            </CardTitle>
            <CardDescription>
              Test message capture and user context functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.message)}
                  <span className="text-sm">Capture Message</span>
                </div>
                <Button size="sm" onClick={testCaptureMessage}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.user)}
                  <span className="text-sm">Set User</span>
                </div>
                <Button size="sm" onClick={testSetUser}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.clearUser)}
                  <span className="text-sm">Clear User</span>
                </div>
                <Button size="sm" onClick={testClearUser} variant="outline">
                  Test
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance and Features Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance and Features Tests
            </CardTitle>
            <CardDescription>
              Test performance monitoring and advanced features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.performance)}
                  <span className="text-sm">Performance</span>
                </div>
                <Button size="sm" onClick={testPerformance}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.replay)}
                  <span className="text-sm">Session Replay</span>
                </div>
                <Button size="sm" onClick={testSessionReplay}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.feedback)}
                  <span className="text-sm">User Feedback</span>
                </div>
                <Button size="sm" onClick={testUserFeedback}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.server)}
                  <span className="text-sm">Server API</span>
                </div>
                <Button size="sm" onClick={testServerAPI}>
                  Test
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Results Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results Summary</CardTitle>
            <CardDescription>
              Overview of all test results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(testResults).map(([test, status]) => (
                <div key={test} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm capitalize">{test}</span>
                  {getStatusBadge(status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-muted-foreground">
            <p>1. Configure your test parameters above</p>
            <p>2. Run each test by clicking the "Test" buttons</p>
            <p>3. Check your Sentry dashboard to verify events are being captured</p>
            <p>4. Look for events in Issues, Performance, and Replays sections</p>
            <p>5. Verify that user context and custom tags are properly set</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
