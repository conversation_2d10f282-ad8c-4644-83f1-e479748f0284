/**
 * Dappier React Hook
 * Provides client-side interface for Dappier AI search and custom copilots
 */

'use client';

import { useState, useCallback, useRef } from 'react';
import { useSentry } from '@/hooks/useSentry';
import type {
  UseDappierOptions,
  UseDappierReturn,
  RealTimeDataRequest,
  RealTimeDataResponse,
  SearchRequest,
  SearchResponse,
  RecommendationsRequest,
  RecommendationsResponse,
  CopilotRequest,
  CopilotResponse,
  DappierError,
} from '@/lib/types/dappier';

/**
 * Default options for the Dappier hook
 */
const defaultOptions: UseDappierOptions = {
  enableCaching: true,
  autoRetry: true,
};

/**
 * Dappier React hook for AI search and custom copilots
 */
export function useDappier(options: UseDappierOptions = {}): UseDappierReturn {
  const config = { ...defaultOptions, ...options };
  const { captureError, addBreadcrumb } = useSentry();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<DappierError | null>(null);
  const [lastResponse, setLastResponse] = useState<any>(null);
  
  const cacheRef = useRef(new Map<string, { data: any; timestamp: number }>());
  const abortControllerRef = useRef<AbortController | null>(null);

  // Check if Dappier is enabled
  const isEnabled = process.env.FEATURE_DAPPIER !== 'false';

  /**
   * Make API request to Dappier
   */
  const makeRequest = useCallback(async <T = any>(
    endpoint: string,
    data: any,
    cacheKey?: string
  ): Promise<T | null> => {
    if (!isEnabled) {
      const error: DappierError = {
        code: 'DAPPIER_DISABLED',
        message: 'Dappier integration is not enabled',
        timestamp: new Date().toISOString(),
      };
      setError(error);
      return null;
    }

    // Check cache first
    if (config.enableCaching && cacheKey) {
      const cached = cacheRef.current.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < 300000) { // 5 minutes
        return cached.data;
      }
    }

    setIsLoading(true);
    setError(null);

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: DappierError = {
          code: `HTTP_${response.status}`,
          message: errorData.message || response.statusText,
          details: errorData,
          timestamp: new Date().toISOString(),
        };
        
        setError(error);
        
        // Capture error in Sentry
        captureError(new Error(error.message), {
          tags: {
            service: 'dappier',
            endpoint,
            statusCode: response.status.toString(),
          },
          extra: {
            error,
            requestData: data,
          },
        });

        return null;
      }

      const result = await response.json();
      
      if (!result.success) {
        const error: DappierError = result.error || {
          code: 'API_ERROR',
          message: 'API request failed',
          timestamp: new Date().toISOString(),
        };
        
        setError(error);
        return null;
      }

      const responseData = result.data;
      setLastResponse(responseData);

      // Cache the result
      if (config.enableCaching && cacheKey) {
        cacheRef.current.set(cacheKey, {
          data: responseData,
          timestamp: Date.now(),
        });
      }

      // Add breadcrumb for successful request
      addBreadcrumb({
        category: 'dappier',
        message: `Successful ${endpoint} request`,
        level: 'info',
        data: {
          endpoint,
          cacheKey,
          responseSize: JSON.stringify(responseData).length,
        },
      });

      // Call success callback
      if (config.onSuccess) {
        config.onSuccess(responseData);
      }

      return responseData;
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled, don't set error
        return null;
      }

      const error: DappierError = {
        code: 'REQUEST_FAILED',
        message: err instanceof Error ? err.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
      
      setError(error);
      
      // Capture error in Sentry
      captureError(err as Error, {
        tags: {
          service: 'dappier',
          endpoint,
        },
        extra: {
          requestData: data,
        },
      });

      // Call error callback
      if (config.onError) {
        config.onError(error);
      }

      return null;
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [isEnabled, config, captureError, addBreadcrumb]);

  /**
   * Query real-time data
   */
  const queryRealTimeData = useCallback(async (
    request: RealTimeDataRequest
  ): Promise<RealTimeDataResponse> => {
    const cacheKey = `realtime:${JSON.stringify(request)}`;
    const result = await makeRequest<RealTimeDataResponse>(
      '/api/dappier',
      { action: 'query', ...request },
      cacheKey
    );
    
    return result || {
      message: 'Failed to get real-time data',
      timestamp: new Date().toISOString(),
    };
  }, [makeRequest]);

  /**
   * Perform AI search
   */
  const search = useCallback(async (
    request: SearchRequest
  ): Promise<SearchResponse> => {
    const cacheKey = `search:${JSON.stringify(request)}`;
    const result = await makeRequest<SearchResponse>(
      '/api/dappier',
      { action: 'search', ...request },
      cacheKey
    );
    
    return result || {
      results: [],
      totalCount: 0,
      query: request.query,
      processingTime: 0,
      suggestions: [],
      facets: [],
    };
  }, [makeRequest]);

  /**
   * Get AI recommendations
   */
  const getRecommendations = useCallback(async (
    request: RecommendationsRequest
  ): Promise<RecommendationsResponse> => {
    const cacheKey = `recommendations:${JSON.stringify(request)}`;
    const result = await makeRequest<RecommendationsResponse>(
      '/api/dappier',
      { action: 'recommendations', ...request },
      cacheKey
    );
    
    return result || {
      recommendations: [],
      totalCount: 0,
      algorithm: 'dappier-ai',
      confidence: 0,
      explanations: [],
    };
  }, [makeRequest]);

  /**
   * Send message to copilot
   */
  const sendMessage = useCallback(async (
    request: CopilotRequest
  ): Promise<CopilotResponse> => {
    // Don't cache copilot messages as they should be unique
    const result = await makeRequest<CopilotResponse>(
      '/api/dappier',
      { action: 'copilot', ...request }
    );
    
    return result || {
      message: 'I apologize, but I couldn\'t process your request at the moment.',
      conversationId: request.conversationId || `conv_${Date.now()}`,
      messageId: `msg_${Date.now()}`,
      type: 'error',
      actions: [],
      suggestions: [],
      confidence: 0,
      sources: [],
    };
  }, [makeRequest]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
    
    addBreadcrumb({
      category: 'dappier',
      message: 'Cache cleared',
      level: 'info',
    });
  }, [addBreadcrumb]);

  return {
    // Core functions
    queryRealTimeData,
    search,
    getRecommendations,
    sendMessage,
    
    // State
    isLoading,
    error,
    lastResponse,
    
    // Utilities
    clearCache,
    isEnabled,
  };
}

/**
 * Hook for simplified real-time data queries
 */
export function useDappierQuery() {
  const { queryRealTimeData, isLoading, error } = useDappier();

  const query = useCallback(async (
    queryText: string,
    options?: Partial<RealTimeDataRequest>
  ) => {
    return await queryRealTimeData({
      query: queryText,
      ...options,
    });
  }, [queryRealTimeData]);

  return {
    query,
    isLoading,
    error,
  };
}

/**
 * Hook for AI search functionality
 */
export function useDappierSearch() {
  const { search, isLoading, error } = useDappier();

  const performSearch = useCallback(async (
    queryText: string,
    options?: Partial<SearchRequest>
  ) => {
    return await search({
      query: queryText,
      type: 'semantic',
      includeSnippets: true,
      includeMetadata: true,
      ...options,
    });
  }, [search]);

  return {
    search: performSearch,
    isLoading,
    error,
  };
}

/**
 * Hook for AI recommendations
 */
export function useDappierRecommendations() {
  const { getRecommendations, isLoading, error } = useDappier();

  const recommend = useCallback(async (
    userId?: string,
    options?: Partial<RecommendationsRequest>
  ) => {
    return await getRecommendations({
      userId,
      type: 'content',
      maxResults: 5,
      includeReasons: true,
      ...options,
    });
  }, [getRecommendations]);

  return {
    recommend,
    isLoading,
    error,
  };
}

/**
 * Hook for custom copilot functionality
 */
export function useDappierCopilot() {
  const { sendMessage, isLoading, error } = useDappier();
  const [conversationId, setConversationId] = useState<string>(`conv_${Date.now()}`);
  const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant'; content: string; timestamp: string }>>([]);

  const chat = useCallback(async (
    message: string,
    options?: Partial<CopilotRequest>
  ) => {
    // Add user message to history
    const userMessage = {
      role: 'user' as const,
      content: message,
      timestamp: new Date().toISOString(),
    };
    setMessages(prev => [...prev, userMessage]);

    const response = await sendMessage({
      message,
      conversationId,
      context: {
        previousMessages: messages.slice(-5), // Last 5 messages for context
      },
      personality: {
        tone: 'friendly',
        responseStyle: 'conversational',
      },
      ...options,
    });

    // Add assistant response to history
    const assistantMessage = {
      role: 'assistant' as const,
      content: response.message,
      timestamp: new Date().toISOString(),
    };
    setMessages(prev => [...prev, assistantMessage]);

    return response;
  }, [sendMessage, conversationId, messages]);

  const clearConversation = useCallback(() => {
    setMessages([]);
    setConversationId(`conv_${Date.now()}`);
  }, []);

  return {
    chat,
    messages,
    conversationId,
    clearConversation,
    isLoading,
    error,
  };
}
