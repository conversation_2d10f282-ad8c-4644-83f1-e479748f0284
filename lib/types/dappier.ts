/**
 * Dappier TypeScript Type Definitions
 * Comprehensive types for Dappier AI search and custom copilots
 */

// =============================================================================
// Core Dappier Types
// =============================================================================

/**
 * Dappier configuration options
 */
export interface DappierConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  maxRetries?: number;
  enableCaching?: boolean;
  cacheTtl?: number;
  defaultModel?: string;
}

/**
 * Dappier API response wrapper
 */
export interface DappierResponse<T = any> {
  success: boolean;
  data?: T;
  error?: DappierError;
  metadata?: DappierMetadata;
}

/**
 * Dappier error information
 */
export interface DappierError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Dappier response metadata
 */
export interface DappierMetadata {
  requestId?: string;
  model?: string;
  processingTime?: number;
  tokensUsed?: number;
  sources?: string[];
  confidence?: number;
}

// =============================================================================
// Real-Time Data API Types
// =============================================================================

/**
 * Real-time data query request
 */
export interface RealTimeDataRequest {
  query: string;
  model?: string;
  context?: Record<string, any>;
  filters?: DataFilter[];
  maxResults?: number;
  includeMetadata?: boolean;
}

/**
 * Real-time data response
 */
export interface RealTimeDataResponse {
  message: string;
  data?: any[];
  sources?: DataSource[];
  confidence?: number;
  timestamp: string;
}

/**
 * Data filter for queries
 */
export interface DataFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan';
  value: any;
}

/**
 * Data source information
 */
export interface DataSource {
  id: string;
  name: string;
  type: 'news' | 'finance' | 'sports' | 'weather' | 'custom';
  url?: string;
  lastUpdated: string;
  reliability?: number;
}

// =============================================================================
// AI Search Types
// =============================================================================

/**
 * AI search request
 */
export interface SearchRequest {
  query: string;
  type?: 'semantic' | 'keyword' | 'hybrid';
  filters?: SearchFilter[];
  sort?: SearchSort;
  pagination?: SearchPagination;
  includeSnippets?: boolean;
  includeMetadata?: boolean;
}

/**
 * AI search response
 */
export interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  query: string;
  processingTime: number;
  suggestions?: string[];
  facets?: SearchFacet[];
}

/**
 * Individual search result
 */
export interface SearchResult {
  id: string;
  title: string;
  content: string;
  snippet?: string;
  url?: string;
  score: number;
  metadata?: Record<string, any>;
  highlights?: SearchHighlight[];
  source?: DataSource;
}

/**
 * Search filter
 */
export interface SearchFilter {
  field: string;
  values: any[];
  operator?: 'and' | 'or' | 'not';
}

/**
 * Search sorting
 */
export interface SearchSort {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Search pagination
 */
export interface SearchPagination {
  page: number;
  limit: number;
  offset?: number;
}

/**
 * Search result highlight
 */
export interface SearchHighlight {
  field: string;
  fragments: string[];
}

/**
 * Search facet for filtering
 */
export interface SearchFacet {
  field: string;
  values: SearchFacetValue[];
}

/**
 * Search facet value
 */
export interface SearchFacetValue {
  value: any;
  count: number;
  selected?: boolean;
}

// =============================================================================
// AI Recommendations Types
// =============================================================================

/**
 * AI recommendations request
 */
export interface RecommendationsRequest {
  userId?: string;
  context?: RecommendationContext;
  type?: 'content' | 'product' | 'action' | 'mixed';
  maxResults?: number;
  includeReasons?: boolean;
  excludeIds?: string[];
}

/**
 * AI recommendations response
 */
export interface RecommendationsResponse {
  recommendations: Recommendation[];
  totalCount: number;
  algorithm?: string;
  confidence?: number;
  explanations?: RecommendationExplanation[];
}

/**
 * Individual recommendation
 */
export interface Recommendation {
  id: string;
  type: 'content' | 'product' | 'action';
  title: string;
  description?: string;
  url?: string;
  imageUrl?: string;
  score: number;
  confidence: number;
  metadata?: Record<string, any>;
  reasons?: string[];
}

/**
 * Recommendation context
 */
export interface RecommendationContext {
  currentPage?: string;
  userHistory?: string[];
  preferences?: Record<string, any>;
  demographics?: UserDemographics;
  sessionData?: Record<string, any>;
}

/**
 * User demographics for recommendations
 */
export interface UserDemographics {
  age?: number;
  gender?: string;
  location?: string;
  interests?: string[];
  language?: string;
}

/**
 * Recommendation explanation
 */
export interface RecommendationExplanation {
  recommendationId: string;
  reason: string;
  confidence: number;
  factors: string[];
}

// =============================================================================
// Custom Copilot Types
// =============================================================================

/**
 * Copilot conversation request
 */
export interface CopilotRequest {
  message: string;
  conversationId?: string;
  userId?: string;
  context?: CopilotContext;
  personality?: CopilotPersonality;
  capabilities?: CopilotCapability[];
}

/**
 * Copilot conversation response
 */
export interface CopilotResponse {
  message: string;
  conversationId: string;
  messageId: string;
  type: 'text' | 'action' | 'suggestion' | 'error';
  actions?: CopilotAction[];
  suggestions?: string[];
  confidence?: number;
  sources?: DataSource[];
}

/**
 * Copilot context information
 */
export interface CopilotContext {
  currentPage?: string;
  userProfile?: UserProfile;
  sessionData?: Record<string, any>;
  previousMessages?: CopilotMessage[];
  integrations?: CopilotIntegration[];
}

/**
 * Copilot personality configuration
 */
export interface CopilotPersonality {
  name?: string;
  tone: 'professional' | 'friendly' | 'casual' | 'formal' | 'humorous';
  expertise?: string[];
  language?: string;
  responseStyle?: 'concise' | 'detailed' | 'conversational';
}

/**
 * Copilot capability
 */
export interface CopilotCapability {
  type: 'search' | 'recommendation' | 'action' | 'integration';
  name: string;
  description?: string;
  enabled: boolean;
  config?: Record<string, any>;
}

/**
 * Copilot action
 */
export interface CopilotAction {
  type: 'navigate' | 'search' | 'recommend' | 'execute' | 'external';
  label: string;
  data: Record<string, any>;
  confirmation?: boolean;
}

/**
 * Copilot message
 */
export interface CopilotMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Copilot integration
 */
export interface CopilotIntegration {
  service: 'revenuecat' | 'elevenlabs' | 'sentry' | 'custom';
  enabled: boolean;
  config?: Record<string, any>;
}

// =============================================================================
// User Profile Types
// =============================================================================

/**
 * User profile for personalization
 */
export interface UserProfile {
  id: string;
  email?: string;
  name?: string;
  preferences?: UserPreferences;
  history?: UserHistory;
  subscription?: UserSubscription;
  demographics?: UserDemographics;
}

/**
 * User preferences
 */
export interface UserPreferences {
  language?: string;
  timezone?: string;
  topics?: string[];
  contentTypes?: string[];
  notificationSettings?: NotificationSettings;
}

/**
 * User history
 */
export interface UserHistory {
  searches?: string[];
  views?: string[];
  interactions?: UserInteraction[];
  lastActive?: string;
}

/**
 * User interaction
 */
export interface UserInteraction {
  type: 'click' | 'view' | 'search' | 'share' | 'like' | 'comment';
  target: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * User subscription information
 */
export interface UserSubscription {
  plan: string;
  status: 'active' | 'inactive' | 'trial' | 'expired';
  features?: string[];
  limits?: Record<string, number>;
}

/**
 * Notification settings
 */
export interface NotificationSettings {
  email?: boolean;
  push?: boolean;
  inApp?: boolean;
  frequency?: 'immediate' | 'daily' | 'weekly' | 'never';
}

// =============================================================================
// Hook Types
// =============================================================================

/**
 * Dappier hook options
 */
export interface UseDappierOptions {
  apiKey?: string;
  baseUrl?: string;
  enableCaching?: boolean;
  defaultModel?: string;
  autoRetry?: boolean;
  onError?: (error: DappierError) => void;
  onSuccess?: (data: any) => void;
}

/**
 * Dappier hook return type
 */
export interface UseDappierReturn {
  // Real-time data
  queryRealTimeData: (request: RealTimeDataRequest) => Promise<RealTimeDataResponse>;
  
  // Search functionality
  search: (request: SearchRequest) => Promise<SearchResponse>;
  
  // Recommendations
  getRecommendations: (request: RecommendationsRequest) => Promise<RecommendationsResponse>;
  
  // Copilot
  sendMessage: (request: CopilotRequest) => Promise<CopilotResponse>;
  
  // State
  isLoading: boolean;
  error: DappierError | null;
  lastResponse: any;
  
  // Utilities
  clearCache: () => void;
  isEnabled: boolean;
}

// =============================================================================
// API Response Types
// =============================================================================

/**
 * Dappier API error response
 */
export interface DappierAPIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Dappier health check response
 */
export interface DappierHealthCheck {
  status: 'healthy' | 'unhealthy';
  apiKey: string;
  baseUrl: string;
  defaultModel?: string;
  lastRequest?: string;
  timestamp: string;
}

// =============================================================================
// Utility Types
// =============================================================================

/**
 * Dappier model types
 */
export type DappierModel = string; // Model IDs start with 'am_'

/**
 * Dappier query types
 */
export type QueryType = 'search' | 'recommendation' | 'realtime' | 'copilot';

/**
 * Dappier content types
 */
export type ContentType = 'news' | 'finance' | 'sports' | 'weather' | 'general' | 'custom';

/**
 * Dappier response formats
 */
export type ResponseFormat = 'json' | 'text' | 'markdown' | 'html';

// =============================================================================
// Export all types
// =============================================================================

export type {
  // Re-export common types for convenience
};
