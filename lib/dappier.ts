/**
 * Dappier Utility Functions
 * Helper functions for Dappier AI search and custom copilots
 */

import type {
  DappierConfig,
  DappierResponse,
  DappierError,
  RealTimeDataRequest,
  RealTimeDataResponse,
  SearchRequest,
  SearchResponse,
  RecommendationsRequest,
  RecommendationsResponse,
  CopilotRequest,
  CopilotResponse,
  DappierHealthCheck,
} from '@/lib/types/dappier';

// =============================================================================
// Configuration Utilities
// =============================================================================

/**
 * Check if Dappier is enabled and properly configured
 */
export function isDappierEnabled(): boolean {
  return !!(
    process.env.DAPPIER_API_KEY &&
    process.env.FEATURE_DAPPIER !== 'false'
  );
}

/**
 * Get Dappier configuration from environment variables
 */
export function getDappierConfig(): DappierConfig {
  return {
    apiKey: process.env.DAPPIER_API_KEY || '',
    baseUrl: process.env.DAPPIER_BASE_URL || 'https://api.dappier.com',
    timeout: parseInt(process.env.DAPPIER_TIMEOUT || '30000'),
    maxRetries: parseInt(process.env.DAPPIER_MAX_RETRIES || '3'),
    enableCaching: process.env.DAPPIER_ENABLE_CACHING === 'true',
    cacheTtl: parseInt(process.env.DAPPIER_CACHE_TTL || '300'),
    defaultModel: process.env.DAPPIER_DEFAULT_MODEL || 'am_01j06ytn18ejftedz6dyhz2b15',
  };
}

/**
 * Validate Dappier configuration
 */
export function validateDappierConfig(config: DappierConfig): boolean {
  return !!(
    config.apiKey &&
    config.baseUrl &&
    config.defaultModel
  );
}

// =============================================================================
// HTTP Client Utilities
// =============================================================================

/**
 * Create HTTP headers for Dappier API requests
 */
export function createDappierHeaders(apiKey: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
    'User-Agent': 'Dappier-NextJS-Client/1.0.0',
  };
}

/**
 * Make HTTP request to Dappier API
 */
export async function makeDappierRequest<T = any>(
  endpoint: string,
  options: RequestInit = {},
  config?: Partial<DappierConfig>
): Promise<DappierResponse<T>> {
  const dappierConfig = { ...getDappierConfig(), ...config };
  
  if (!validateDappierConfig(dappierConfig)) {
    throw new Error('Invalid Dappier configuration');
  }

  const url = `${dappierConfig.baseUrl}${endpoint}`;
  const headers = createDappierHeaders(dappierConfig.apiKey);

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), dappierConfig.timeout);

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error: DappierError = {
        code: `HTTP_${response.status}`,
        message: errorData.message || response.statusText,
        details: errorData,
        timestamp: new Date().toISOString(),
      };
      
      return {
        success: false,
        error,
      };
    }

    const data = await response.json();
    
    return {
      success: true,
      data,
      metadata: {
        requestId: response.headers.get('x-request-id') || undefined,
        processingTime: parseInt(response.headers.get('x-processing-time') || '0'),
      },
    };
  } catch (error) {
    const dappierError: DappierError = {
      code: 'REQUEST_FAILED',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: { error },
      timestamp: new Date().toISOString(),
    };

    return {
      success: false,
      error: dappierError,
    };
  }
}

/**
 * Retry failed requests with exponential backoff
 */
export async function retryDappierRequest<T = any>(
  requestFn: () => Promise<DappierResponse<T>>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<DappierResponse<T>> {
  let lastError: DappierError | undefined;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await requestFn();
      
      if (result.success) {
        return result;
      }
      
      lastError = result.error;
      
      // Don't retry on client errors (4xx)
      if (lastError?.code.startsWith('HTTP_4')) {
        break;
      }
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      lastError = {
        code: 'RETRY_FAILED',
        message: error instanceof Error ? error.message : 'Retry failed',
        timestamp: new Date().toISOString(),
      };
    }
  }

  return {
    success: false,
    error: lastError || {
      code: 'MAX_RETRIES_EXCEEDED',
      message: 'Maximum retry attempts exceeded',
      timestamp: new Date().toISOString(),
    },
  };
}

// =============================================================================
// Real-Time Data API
// =============================================================================

/**
 * Query real-time data from Dappier
 */
export async function queryRealTimeData(
  request: RealTimeDataRequest,
  config?: Partial<DappierConfig>
): Promise<DappierResponse<RealTimeDataResponse>> {
  const dappierConfig = { ...getDappierConfig(), ...config };
  const model = request.model || dappierConfig.defaultModel;
  
  const endpoint = `/app/aimodel/${model}`;
  
  const requestBody = {
    query: request.query,
    ...(request.context && { context: request.context }),
    ...(request.maxResults && { max_results: request.maxResults }),
    ...(request.includeMetadata && { include_metadata: request.includeMetadata }),
  };

  if (dappierConfig.maxRetries && dappierConfig.maxRetries > 0) {
    return retryDappierRequest(
      () => makeDappierRequest<RealTimeDataResponse>(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      }, config),
      dappierConfig.maxRetries
    );
  }

  return makeDappierRequest<RealTimeDataResponse>(endpoint, {
    method: 'POST',
    body: JSON.stringify(requestBody),
  }, config);
}

// =============================================================================
// AI Search Functions
// =============================================================================

/**
 * Perform AI-powered search
 */
export async function performSearch(
  request: SearchRequest,
  config?: Partial<DappierConfig>
): Promise<DappierResponse<SearchResponse>> {
  // For now, we'll use the real-time data API for search
  // This can be extended when Dappier provides dedicated search endpoints
  const realTimeRequest: RealTimeDataRequest = {
    query: `Search for: ${request.query}`,
    maxResults: request.pagination?.limit || 10,
    includeMetadata: request.includeMetadata,
  };

  const response = await queryRealTimeData(realTimeRequest, config);
  
  if (!response.success) {
    return response;
  }

  // Transform real-time data response to search response format
  const searchResponse: SearchResponse = {
    results: [], // Would be populated from response.data
    totalCount: 0,
    query: request.query,
    processingTime: response.metadata?.processingTime || 0,
    suggestions: [],
    facets: [],
  };

  return {
    success: true,
    data: searchResponse,
    metadata: response.metadata,
  };
}

// =============================================================================
// AI Recommendations Functions
// =============================================================================

/**
 * Get AI recommendations
 */
export async function getRecommendations(
  request: RecommendationsRequest,
  config?: Partial<DappierConfig>
): Promise<DappierResponse<RecommendationsResponse>> {
  // Use real-time data API for recommendations
  const query = `Get ${request.type || 'content'} recommendations${
    request.userId ? ` for user ${request.userId}` : ''
  }${request.context ? ` based on context: ${JSON.stringify(request.context)}` : ''}`;

  const realTimeRequest: RealTimeDataRequest = {
    query,
    maxResults: request.maxResults || 5,
    includeMetadata: true,
  };

  const response = await queryRealTimeData(realTimeRequest, config);
  
  if (!response.success) {
    return response;
  }

  // Transform response to recommendations format
  const recommendationsResponse: RecommendationsResponse = {
    recommendations: [], // Would be populated from response.data
    totalCount: 0,
    algorithm: 'dappier-ai',
    confidence: 0.8,
    explanations: [],
  };

  return {
    success: true,
    data: recommendationsResponse,
    metadata: response.metadata,
  };
}

// =============================================================================
// Custom Copilot Functions
// =============================================================================

/**
 * Send message to custom copilot
 */
export async function sendCopilotMessage(
  request: CopilotRequest,
  config?: Partial<DappierConfig>
): Promise<DappierResponse<CopilotResponse>> {
  // Enhance the query with context and personality
  let enhancedQuery = request.message;
  
  if (request.personality) {
    enhancedQuery = `[Personality: ${request.personality.tone}, Style: ${request.personality.responseStyle || 'conversational'}] ${enhancedQuery}`;
  }
  
  if (request.context) {
    enhancedQuery += ` [Context: ${JSON.stringify(request.context)}]`;
  }

  const realTimeRequest: RealTimeDataRequest = {
    query: enhancedQuery,
    includeMetadata: true,
  };

  const response = await queryRealTimeData(realTimeRequest, config);
  
  if (!response.success) {
    return response;
  }

  // Transform response to copilot format
  const copilotResponse: CopilotResponse = {
    message: response.data?.message || 'I apologize, but I couldn\'t process your request.',
    conversationId: request.conversationId || `conv_${Date.now()}`,
    messageId: `msg_${Date.now()}`,
    type: 'text',
    actions: [],
    suggestions: [],
    confidence: response.metadata?.confidence || 0.8,
    sources: response.data?.sources || [],
  };

  return {
    success: true,
    data: copilotResponse,
    metadata: response.metadata,
  };
}

// =============================================================================
// Health Check and Utilities
// =============================================================================

/**
 * Test Dappier connection and configuration
 */
export async function testDappierConnection(
  config?: Partial<DappierConfig>
): Promise<DappierResponse<DappierHealthCheck>> {
  try {
    const testRequest: RealTimeDataRequest = {
      query: 'Hello, this is a connection test.',
    };

    const response = await queryRealTimeData(testRequest, config);
    const dappierConfig = { ...getDappierConfig(), ...config };
    
    const healthCheck: DappierHealthCheck = {
      status: response.success ? 'healthy' : 'unhealthy',
      apiKey: dappierConfig.apiKey ? '***configured***' : 'missing',
      baseUrl: dappierConfig.baseUrl,
      defaultModel: dappierConfig.defaultModel,
      lastRequest: new Date().toISOString(),
      timestamp: new Date().toISOString(),
    };

    return {
      success: true,
      data: healthCheck,
    };
  } catch (error) {
    const healthCheck: DappierHealthCheck = {
      status: 'unhealthy',
      apiKey: 'error',
      baseUrl: 'error',
      timestamp: new Date().toISOString(),
    };

    return {
      success: false,
      data: healthCheck,
      error: {
        code: 'CONNECTION_TEST_FAILED',
        message: error instanceof Error ? error.message : 'Connection test failed',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

// =============================================================================
// Cache Utilities
// =============================================================================

/**
 * Simple in-memory cache for Dappier responses
 */
class DappierCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 300): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// Global cache instance
export const dappierCache = new DappierCache();

/**
 * Create cache key for requests
 */
export function createCacheKey(endpoint: string, params: any): string {
  return `dappier:${endpoint}:${JSON.stringify(params)}`;
}
