/**
 * RevenueCat TypeScript Type Definitions
 * Based on RevenueCat REST API v1 and JavaScript SDK
 * Documentation: https://www.revenuecat.com/docs/api-v1
 */

// Base types
export interface RevenueCatError {
  code: number;
  message: string;
  attribute_errors?: Record<string, string[]>;
}

export interface RevenueCatResponse<T> {
  request_date: string;
  request_date_ms: number;
  subscriber?: T;
}

// Customer/Subscriber types
export interface Subscriber {
  app_user_id: string;
  original_app_user_id: string;
  original_application_version?: string;
  original_purchase_date?: string;
  request_date: string;
  request_date_ms: number;
  first_seen: string;
  last_seen: string;
  management_url?: string;
  non_subscriptions: Record<string, NonSubscription[]>;
  subscriptions: Record<string, Subscription>;
  entitlements: Record<string, Entitlement>;
}

export interface Subscription {
  auto_resume_date?: string;
  billing_issues_detected_at?: string;
  expires_date: string;
  grace_period_expires_date?: string;
  is_sandbox: boolean;
  original_purchase_date: string;
  ownership_type: 'PURCHASED' | 'FAMILY_SHARED';
  period_type: 'NORMAL' | 'TRIAL' | 'INTRO';
  product_identifier: string;
  purchase_date: string;
  refunded_at?: string;
  store: 'APP_STORE' | 'MAC_APP_STORE' | 'PLAY_STORE' | 'STRIPE' | 'PROMOTIONAL';
  unsubscribe_detected_at?: string;
}

export interface NonSubscription {
  id: string;
  is_sandbox: boolean;
  original_purchase_date: string;
  product_identifier: string;
  purchase_date: string;
  store: 'APP_STORE' | 'MAC_APP_STORE' | 'PLAY_STORE' | 'STRIPE' | 'PROMOTIONAL';
}

export interface Entitlement {
  expires_date?: string;
  grace_period_expires_date?: string;
  product_identifier: string;
  purchase_date: string;
}

// Offerings types
export interface OfferingsResponse {
  current_offering_id?: string;
  offerings: Offering[];
}

export interface Offering {
  description: string;
  identifier: string;
  metadata?: Record<string, unknown>;
  packages: Package[];
}

export interface Package {
  identifier: string;
  platform_product_identifier: string;
}

// Attribution types
export interface AttributionData {
  data: Record<string, unknown>;
  from: 'APPLE_SEARCH_ADS' | 'ADJUST' | 'APPSFLYER' | 'BRANCH' | 'TENJIN' | 'FACEBOOK' | 'MPARTICLE';
  network_user_id?: string;
}

// Receipt types
export interface ReceiptRequest {
  app_user_id: string;
  fetch_token: string;
  attributes?: Record<string, string>;
  normal_duration?: string;
  intro_duration?: string;
  trial_duration?: string;
}

// API Request/Response types
export interface GetCustomerInfoRequest {
  app_user_id: string;
}

export interface GetOfferingsRequest {
  app_user_id: string;
}

export interface AddAttributionRequest {
  app_user_id: string;
  attribution_data: AttributionData;
}

export type ProcessReceiptRequest = ReceiptRequest;

// Hook types for client-side usage
export interface UseRevenueCatOptions {
  apiKey?: string;
  userId?: string;
  enableLogging?: boolean;
}

export interface UseRevenueCatReturn {
  customerInfo: Subscriber | null;
  offerings: Offering[] | null;
  isLoading: boolean;
  error: RevenueCatError | null;
  refreshCustomerInfo: () => Promise<void>;
  refreshOfferings: () => Promise<void>;
  purchasePackage: (packageIdentifier: string) => Promise<void>;
}

// Environment configuration
export interface RevenueCatConfig {
  apiKey: string;
  secretKey: string;
  baseUrl: string;
  isProduction: boolean;
}

// API Response wrappers
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: RevenueCatError;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  has_more: boolean;
  next_page?: string;
}

// Webhook types (for future use)
export interface WebhookEvent {
  api_version: string;
  event: {
    aliases: string[];
    app_id: string;
    app_user_id: string;
    commission_percentage?: number;
    country_code?: string;
    currency?: string;
    entitlement_id?: string;
    entitlement_ids?: string[];
    environment: 'SANDBOX' | 'PRODUCTION';
    event_timestamp_ms: number;
    expiration_at_ms?: number;
    id: string;
    is_family_share?: boolean;
    offer_code?: string;
    original_app_user_id: string;
    original_transaction_id?: string;
    period_type?: 'NORMAL' | 'TRIAL' | 'INTRO';
    presented_offering_id?: string;
    price?: number;
    price_in_purchased_currency?: number;
    product_id?: string;
    purchased_at_ms?: number;
    store?: string;
    subscriber_attributes?: Record<string, unknown>;
    takehome_percentage?: number;
    transaction_id?: string;
    type: string;
  };
}

// Utility types
export type RevenueCatStore = 'APP_STORE' | 'MAC_APP_STORE' | 'PLAY_STORE' | 'STRIPE' | 'PROMOTIONAL';
export type RevenueCatEnvironment = 'SANDBOX' | 'PRODUCTION';
export type RevenueCatPeriodType = 'NORMAL' | 'TRIAL' | 'INTRO';
export type RevenueCatOwnershipType = 'PURCHASED' | 'FAMILY_SHARED';

// Error codes (common RevenueCat error codes)
export enum RevenueCatErrorCode {
  UNKNOWN_ERROR = 0,
  PURCHASE_CANCELLED = 1,
  STORE_PROBLEM = 2,
  PURCHASE_NOT_ALLOWED = 3,
  PURCHASE_INVALID = 4,
  PRODUCT_NOT_AVAILABLE = 5,
  PRODUCT_ALREADY_PURCHASED = 6,
  RECEIPT_ALREADY_IN_USE = 7,
  INVALID_RECEIPT = 8,
  MISSING_RECEIPT_FILE = 9,
  NETWORK_ERROR = 10,
  INVALID_CREDENTIALS = 11,
  UNEXPECTED_BACKEND_RESPONSE = 12,
  RECEIPT_IN_USE_BY_OTHER_SUBSCRIBER = 13,
  INVALID_APP_USER_ID = 14,
  OPERATION_ALREADY_IN_PROGRESS = 15,
  UNKNOWN_BACKEND_ERROR = 16,
  INVALID_APPLE_SUBSCRIPTION_KEY = 17,
  INELIGIBLE_ERROR = 18,
  INSUFFICIENT_PERMISSIONS = 19,
  PAYMENT_PENDING = 20,
  INVALID_SUBSCRIBER_ATTRIBUTES = 21,
  LOG_OUT_CALLED_WITH_ANONYMOUS_USER = 22,
  CONFIGURATION_ERROR = 23,
  UNSUPPORTED_ERROR = 24,
  EMPTY_SUBSCRIBER_ATTRIBUTES = 25,
  PRODUCT_DISCOUNT_MISSING_IDENTIFIER = 26,
  MISSING_APP_USER_ID = 27,
  PRODUCT_DISCOUNT_MISSING_BASE_PLAN = 28,
  UNKNOWN_SUBSCRIPTION_GROUP = 29,
  BILLING_FEATURE_NOT_SUPPORTED = 30,
  CUSTOMER_INFO_ERROR = 31,
}
