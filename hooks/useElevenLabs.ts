/**
 * ElevenLabs React Hook
 * Provides client-side interface for ElevenLabs text-to-speech operations
 * Uses REST API calls for reliable cross-platform compatibility
 */

'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import {
  Voice,
  Model,
  User,
  TextToSpeechRequest,
  UseElevenLabsOptions,
  UseElevenLabsReturn,
  ElevenLabsError,
  ELEVENLABS_MODELS,
} from '@/lib/types/elevenlabs';

// ElevenLabs API configuration
const ELEVENLABS_API_BASE = 'https://api.elevenlabs.io/v1';

// API Error Handler
const handleAPIError = (error: unknown): ElevenLabsError => {
  if (error && typeof error === 'object' && 'message' in error) {
    return {
      code: (error as any).status || (error as any).statusCode || 0,
      message: (error as Error).message,
      detail: (error as any).detail,
    };
  }
  return {
    code: 0,
    message: error instanceof Error ? error.message : 'Unknown error occurred',
  };
};

// API request helper
const makeAPIRequest = async (
  endpoint: string,
  options: RequestInit = {},
  apiKey?: string
): Promise<any> => {
  const effectiveApiKey = apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;

  if (!effectiveApiKey) {
    throw new Error('ElevenLabs API key is required');
  }

  const response = await fetch(`${ELEVENLABS_API_BASE}${endpoint}`, {
    ...options,
    headers: {
      'xi-api-key': effectiveApiKey,
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      status: response.status,
      message: errorData.detail?.message || response.statusText,
      detail: errorData.detail,
    };
  }

  return response;
};

/**
 * Main ElevenLabs hook using REST API
 */
export function useElevenLabs(options: UseElevenLabsOptions = {}): UseElevenLabsReturn {
  const { apiKey, enableLogging = false } = options;

  const [voices, setVoices] = useState<Voice[] | null>(null);
  const [models, setModels] = useState<Model[] | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ElevenLabsError | null>(null);

  // Get API key from environment if not provided
  const effectiveApiKey = apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;

  // Validate API key
  useEffect(() => {
    if (!effectiveApiKey) {
      setError({
        code: 401,
        message: 'ElevenLabs API key is required',
      });
      return;
    }

    setError(null);
    if (enableLogging) {
      console.log('ElevenLabs API key configured successfully');
    }
  }, [effectiveApiKey, enableLogging]);

  // Refresh voices using REST API
  const refreshVoices = useCallback(async (): Promise<void> => {
    if (!effectiveApiKey) {
      throw new Error('ElevenLabs API key is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await makeAPIRequest('/voices', {}, effectiveApiKey);
      const voicesData = await response.json();

      setVoices(voicesData.voices || []);

      if (enableLogging) {
        console.log('Voices refreshed:', voicesData.voices?.length || 0, 'voices found');
      }
    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [effectiveApiKey, enableLogging]);

  // Refresh models using REST API
  const refreshModels = useCallback(async (): Promise<void> => {
    if (!effectiveApiKey) {
      throw new Error('ElevenLabs API key is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await makeAPIRequest('/models', {}, effectiveApiKey);
      const modelsData = await response.json();

      setModels(modelsData || []);

      if (enableLogging) {
        console.log('Models refreshed:', modelsData?.length || 0, 'models found');
      }
    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [effectiveApiKey, enableLogging]);

  // Refresh user info using REST API
  const refreshUser = useCallback(async (): Promise<void> => {
    if (!effectiveApiKey) {
      throw new Error('ElevenLabs API key is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await makeAPIRequest('/user', {}, effectiveApiKey);
      const userData = await response.json();

      setUser(userData);

      if (enableLogging) {
        console.log('User info refreshed:', userData);
      }
    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [effectiveApiKey, enableLogging]);

  // Generate speech using REST API
  const generateSpeech = useCallback(async (
    voiceId: string,
    request: TextToSpeechRequest
  ): Promise<ArrayBuffer> => {
    if (!effectiveApiKey) {
      throw new Error('ElevenLabs API key is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      if (enableLogging) {
        console.log('Generating speech for voice:', voiceId, 'with text:', request.text.substring(0, 50) + '...');
      }

      const response = await makeAPIRequest(`/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: request.text,
          model_id: request.model_id || ELEVENLABS_MODELS.MULTILINGUAL_V2,
          voice_settings: request.voice_settings,
          language_code: request.language_code,
          pronunciation_dictionary_locators: request.pronunciation_dictionary_locators,
          seed: request.seed,
          previous_text: request.previous_text,
          next_text: request.next_text,
          previous_request_ids: request.previous_request_ids,
          next_request_ids: request.next_request_ids,
          apply_text_normalization: request.apply_text_normalization,
          apply_language_text_normalization: request.apply_language_text_normalization,
          use_pvc_as_ivc: request.use_pvc_as_ivc,
        }),
      }, effectiveApiKey);

      const audioBuffer = await response.arrayBuffer();

      if (enableLogging) {
        console.log('Speech generated successfully, size:', audioBuffer.byteLength, 'bytes');
      }

      return audioBuffer;

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [effectiveApiKey, enableLogging]);

  // Stream speech using REST API (simplified - returns response stream)
  const streamSpeech = useCallback(async (
    voiceId: string,
    request: TextToSpeechRequest
  ): Promise<ReadableStream> => {
    if (!effectiveApiKey) {
      throw new Error('ElevenLabs API key is required');
    }

    try {
      setError(null);

      if (enableLogging) {
        console.log('Starting speech stream for voice:', voiceId);
      }

      const response = await makeAPIRequest(`/text-to-speech/${voiceId}/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: request.text,
          model_id: request.model_id || ELEVENLABS_MODELS.MULTILINGUAL_V2,
          voice_settings: request.voice_settings,
          language_code: request.language_code,
        }),
      }, effectiveApiKey);

      if (enableLogging) {
        console.log('Speech stream started successfully');
      }

      return response.body!;

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    }
  }, [effectiveApiKey, enableLogging]);

  // Auto-load data when API key is available
  useEffect(() => {
    if (effectiveApiKey && !error) {
      refreshVoices().catch(err => {
        if (enableLogging) {
          console.warn('Failed to auto-load voices:', err);
        }
      });
      refreshModels().catch(err => {
        if (enableLogging) {
          console.warn('Failed to auto-load models:', err);
        }
      });
      refreshUser().catch(err => {
        if (enableLogging) {
          console.warn('Failed to auto-load user info:', err);
        }
      });
    }
  }, [effectiveApiKey, refreshVoices, refreshModels, refreshUser, enableLogging, error]);

  return {
    voices,
    models,
    user,
    isLoading,
    error,
    refreshVoices,
    refreshModels,
    refreshUser,
    generateSpeech,
    streamSpeech,
  };
}

/**
 * Hook for text-to-speech operations
 */
export function useTextToSpeech() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<ElevenLabsError | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const generateAndPlay = useCallback(async (
    voiceId: string,
    text: string,
    options: Partial<TextToSpeechRequest> = {}
  ): Promise<string> => {
    try {
      setIsGenerating(true);
      setError(null);

      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!apiKey) {
        throw new Error('ElevenLabs API key is required');
      }

      const response = await makeAPIRequest(`/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          model_id: options.model_id || ELEVENLABS_MODELS.MULTILINGUAL_V2,
          voice_settings: options.voice_settings,
          language_code: options.language_code,
        }),
      }, apiKey);

      const audioBuffer = await response.arrayBuffer();
      const audioBlob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);

      return url;

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const clearAudio = useCallback(() => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
  }, [audioUrl]);

  return {
    generateAndPlay,
    clearAudio,
    isGenerating,
    error,
    audioUrl,
  };
}

/**
 * Hook for voice management
 */
export function useVoiceManagement() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ElevenLabsError | null>(null);

  const cloneVoice = useCallback(async (
    name: string,
    files: File[],
    description?: string
  ): Promise<string> => {
    try {
      setIsLoading(true);
      setError(null);

      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!apiKey) {
        throw new Error('ElevenLabs API key is required');
      }

      const formData = new FormData();
      formData.append('name', name);
      if (description) {
        formData.append('description', description);
      }
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });

      const response = await fetch(`${ELEVENLABS_API_BASE}/voices/add`, {
        method: 'POST',
        headers: {
          'xi-api-key': apiKey,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Voice cloning failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.voice_id;

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteVoice = useCallback(async (voiceId: string): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!apiKey) {
        throw new Error('ElevenLabs API key is required');
      }

      const response = await makeAPIRequest(`/voices/${voiceId}`, {
        method: 'DELETE',
      }, apiKey);

      if (!response.ok) {
        throw new Error(`Voice deletion failed: ${response.statusText}`);
      }

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const editVoice = useCallback(async (
    voiceId: string,
    name?: string,
    description?: string
  ): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!apiKey) {
        throw new Error('ElevenLabs API key is required');
      }

      const response = await makeAPIRequest(`/voices/${voiceId}/edit`, {
        method: 'POST',
        body: JSON.stringify({
          name,
          description,
        }),
      }, apiKey);

      if (!response.ok) {
        throw new Error(`Voice editing failed: ${response.statusText}`);
      }

    } catch (err) {
      const error = handleAPIError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    cloneVoice,
    deleteVoice,
    editVoice,
    isLoading,
    error,
  };
}

/**
 * Hook for audio playback utilities
 */
export function useAudioPlayer() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const play = useCallback(async (audioUrl: string): Promise<void> => {
    try {
      if (audioRef.current) {
        audioRef.current.pause();
      }

      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      audio.addEventListener('loadedmetadata', () => {
        setDuration(audio.duration);
      });

      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime);
      });

      audio.addEventListener('ended', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });

      audio.addEventListener('error', (e) => {
        console.error('Audio playback error:', e);
        setIsPlaying(false);
      });

      await audio.play();
      setIsPlaying(true);

    } catch (err) {
      console.error('Failed to play audio:', err);
      setIsPlaying(false);
    }
  }, []);

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, []);

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      setCurrentTime(0);
    }
  }, []);

  const seek = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  return {
    play,
    pause,
    stop,
    seek,
    isPlaying,
    currentTime,
    duration,
  };
}
