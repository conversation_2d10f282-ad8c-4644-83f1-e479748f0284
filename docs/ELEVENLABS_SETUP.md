# ElevenLabs Integration Setup Guide

This guide covers the complete setup and usage of the ElevenLabs text-to-speech integration in your Next.js 14 App Router application.

## Overview

ElevenLabs is a leading AI voice technology platform that provides realistic text-to-speech conversion. This integration provides:

- **Client-side JavaScript SDK** for primary operations (text-to-speech, voice management, streaming)
- **Server-side API routes** as fallback for admin operations and webhooks
- **React hooks** that prioritize SDK usage with comprehensive error handling
- **TypeScript support** with complete type definitions
- **Audio playback utilities** with built-in player controls
- **Voice cloning and management** capabilities

## Prerequisites

1. **ElevenLabs Account**: Sign up at [elevenlabs.io](https://elevenlabs.io)
2. **API Key**: Get your API key from the ElevenLabs dashboard
3. **Subscription**: Some features require paid subscription tiers

## Installation

The ElevenLabs dependencies are already included in the project:

```bash
pnpm install
```

## Environment Configuration

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Configure your ElevenLabs credentials in `.env.local`:

```env
# ElevenLabs Configuration (SDK-first approach)
# Primary: API key for client-side SDK operations
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Fallback: API key for server-side operations only
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
```

### Getting Your API Key

1. Go to [ElevenLabs Dashboard](https://elevenlabs.io/app)
2. Navigate to **Settings** → **API Keys**
3. Copy your API key
4. Use the same key for both environment variables (client and server)

## Client-Side Usage (SDK-First)

### Basic Text-to-Speech

```typescript
import { useTextToSpeech, useAudioPlayer } from '@/hooks/useElevenLabs';

function TextToSpeechComponent() {
  const { generateAndPlay, isGenerating, error, audioUrl } = useTextToSpeech();
  const { play, pause, isPlaying } = useAudioPlayer();

  const handleSpeak = async () => {
    try {
      const audioUrl = await generateAndPlay(
        'voice_id_here', 
        'Hello, this is ElevenLabs speaking!',
        {
          model_id: 'eleven_multilingual_v2',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.75,
          },
        }
      );
      
      // Auto-play the generated audio
      await play(audioUrl);
    } catch (err) {
      console.error('Speech generation failed:', err);
    }
  };

  return (
    <div>
      <button onClick={handleSpeak} disabled={isGenerating}>
        {isGenerating ? 'Generating...' : 'Speak Text'}
      </button>
      
      {audioUrl && (
        <button onClick={() => isPlaying ? pause() : play(audioUrl)}>
          {isPlaying ? 'Pause' : 'Play'}
        </button>
      )}
      
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

### Voice Management

```typescript
import { useElevenLabs, useVoiceManagement } from '@/hooks/useElevenLabs';

function VoiceManagerComponent() {
  const { voices, refreshVoices, isLoading } = useElevenLabs();
  const { cloneVoice, deleteVoice, editVoice } = useVoiceManagement();

  const handleCloneVoice = async (files: File[]) => {
    try {
      const voiceId = await cloneVoice(
        'My Custom Voice',
        files,
        'A cloned voice for my application'
      );
      console.log('Voice cloned successfully:', voiceId);
      await refreshVoices(); // Refresh the voice list
    } catch (err) {
      console.error('Voice cloning failed:', err);
    }
  };

  return (
    <div>
      <h2>Available Voices</h2>
      {voices?.map(voice => (
        <div key={voice.voice_id}>
          <h3>{voice.name}</h3>
          <p>{voice.description}</p>
          <span>Category: {voice.category}</span>
          
          <button onClick={() => editVoice(voice.voice_id, 'New Name')}>
            Edit
          </button>
          <button onClick={() => deleteVoice(voice.voice_id)}>
            Delete
          </button>
        </div>
      ))}
    </div>
  );
}
```

### Streaming Audio

```typescript
import { useElevenLabs } from '@/hooks/useElevenLabs';

function StreamingComponent() {
  const { streamSpeech } = useElevenLabs();

  const handleStreamSpeech = async () => {
    try {
      const audioStream = await streamSpeech('voice_id_here', {
        text: 'This is streaming text-to-speech',
        model_id: 'eleven_flash_v2_5', // Use Flash for low latency
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.75,
        },
      });

      // Handle the stream
      const reader = audioStream.getReader();
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        // Process audio chunks as they arrive
        console.log('Received audio chunk:', value.length, 'bytes');
      }
    } catch (err) {
      console.error('Streaming failed:', err);
    }
  };

  return (
    <button onClick={handleStreamSpeech}>
      Start Streaming
    </button>
  );
}
```

## Available Models

ElevenLabs provides several models optimized for different use cases:

### Primary Models

1. **Multilingual v2** (`eleven_multilingual_v2`) - **Recommended**
   - Best stability and language diversity
   - Supports 29 languages
   - Excellent accent accuracy

2. **Flash v2.5** (`eleven_flash_v2_5`)
   - Ultra-low latency (50% faster)
   - Supports 32 languages
   - 50% lower cost per character

3. **Turbo v2.5** (`eleven_turbo_v2_5`)
   - Balanced quality and speed
   - Ideal for real-time applications
   - Supports 32 languages

```typescript
import { ELEVENLABS_MODELS } from '@/lib/types/elevenlabs';

// Use in your requests
const audioUrl = await generateAndPlay(voiceId, text, {
  model_id: ELEVENLABS_MODELS.FLASH_V2_5, // For low latency
  // or
  model_id: ELEVENLABS_MODELS.MULTILINGUAL_V2, // For best quality
});
```

## Voice Settings

Fine-tune voice output with these settings:

```typescript
const voiceSettings = {
  stability: 0.5,        // 0-1: Lower = more variable, Higher = more stable
  similarity_boost: 0.75, // 0-1: How closely to match the original voice
  style: 0.2,            // 0-1: Exaggeration of the style (if supported)
  use_speaker_boost: true, // Boost the similarity to the speaker
};
```

## Error Handling

The integration provides comprehensive error handling:

```typescript
import { ElevenLabsErrorCode } from '@/lib/types/elevenlabs';

function handleElevenLabsError(error: ElevenLabsError) {
  switch (error.code) {
    case ElevenLabsErrorCode.UNAUTHORIZED:
      // Handle authentication issues
      console.error('Invalid API key');
      break;
    case ElevenLabsErrorCode.TOO_MANY_REQUESTS:
      // Handle rate limiting
      console.error('Rate limit exceeded');
      break;
    case ElevenLabsErrorCode.UNPROCESSABLE_ENTITY:
      // Handle validation errors
      console.error('Invalid request data');
      break;
    default:
      console.error('ElevenLabs error:', error.message);
  }
}
```

## Server-Side Operations

For admin operations and webhooks, use the server-side API:

```typescript
// GET /api/elevenlabs?action=user-usage
const response = await fetch('/api/elevenlabs?action=user-usage');
const usage = await response.json();

// POST /api/elevenlabs (delete history item)
const response = await fetch('/api/elevenlabs', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'delete-history-item',
    history_item_id: 'item_id_here',
  }),
});
```

## Audio Playback

Use the built-in audio player for seamless playback:

```typescript
import { useAudioPlayer } from '@/hooks/useElevenLabs';

function AudioPlayerComponent() {
  const {
    play,
    pause,
    stop,
    seek,
    isPlaying,
    currentTime,
    duration,
  } = useAudioPlayer();

  return (
    <div>
      <button onClick={() => play(audioUrl)}>Play</button>
      <button onClick={pause}>Pause</button>
      <button onClick={stop}>Stop</button>
      
      <div>
        {Math.floor(currentTime)}s / {Math.floor(duration)}s
      </div>
      
      <input
        type="range"
        min={0}
        max={duration}
        value={currentTime}
        onChange={(e) => seek(Number(e.target.value))}
      />
    </div>
  );
}
```

## Language Support

ElevenLabs supports 32 languages. Specify language for better pronunciation:

```typescript
const audioUrl = await generateAndPlay(voiceId, text, {
  language_code: 'en', // English
  // Other supported codes: 'es', 'fr', 'de', 'it', 'pt', 'ja', 'zh', etc.
});
```

## Production Considerations

### Rate Limiting
- Free tier: 10,000 characters/month
- Paid tiers: Higher limits based on subscription
- Implement proper error handling for rate limits

### Caching
- Cache generated audio files to reduce API calls
- Use browser storage for frequently used audio

### Security
- Never expose server-side API keys to the client
- Validate all user inputs before sending to ElevenLabs
- Implement proper authentication for voice management operations

### Performance
- Use Flash v2.5 model for real-time applications
- Implement streaming for long texts
- Preload commonly used audio files

## Testing

Test the integration using the example component:

```typescript
import { ElevenLabsExample } from '@/components/examples/ElevenLabsExample';

function TestPage() {
  return <ElevenLabsExample />;
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify API key is correct and has proper permissions
2. **Rate Limiting**: Implement exponential backoff and respect rate limits
3. **Audio Playback**: Ensure browser supports audio playback and user has interacted with page
4. **CORS Issues**: Use server-side routes for operations that require CORS handling

### Debug Mode

Enable debug logging:

```typescript
const { voices } = useElevenLabs({
  enableLogging: true, // Enable debug logs
});
```

## Support

- **ElevenLabs Documentation**: [elevenlabs.io/docs](https://elevenlabs.io/docs)
- **API Reference**: [elevenlabs.io/docs/api-reference](https://elevenlabs.io/docs/api-reference)
- **Community Discord**: [discord.gg/elevenlabs](https://discord.gg/elevenlabs)

## Next Steps

After setting up ElevenLabs, you can:

1. Integrate with other AI services for complete voice workflows
2. Implement voice cloning for personalized experiences
3. Add real-time voice chat capabilities
4. Create voice-enabled applications and chatbots
5. Build audio content generation pipelines
