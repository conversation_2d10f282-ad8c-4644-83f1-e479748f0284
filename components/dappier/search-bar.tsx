/**
 * Dappier AI Search Bar Component
 * Intelligent search component with real-time suggestions and results
 */

'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { useDappierSearch } from '@/hooks/useDappier';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Loader2, 
  Sparkles, 
  Clock, 
  TrendingUp,
  X,
  ArrowRight,
  Filter,
  SortAsc
} from 'lucide-react';
import type { SearchResult, SearchFilter } from '@/lib/types/dappier';

interface SearchBarProps {
  placeholder?: string;
  showSuggestions?: boolean;
  showFilters?: boolean;
  maxResults?: number;
  onResultSelect?: (result: SearchResult) => void;
  onSearch?: (query: string, results: SearchResult[]) => void;
  className?: string;
}

export function SearchBar({
  placeholder = "Search with AI...",
  showSuggestions = true,
  showFilters = false,
  maxResults = 10,
  onResultSelect,
  onSearch,
  className = "",
}: SearchBarProps) {
  const { search, isLoading, error } = useDappierSearch();
  
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [filters, setFilters] = useState<SearchFilter[]>([]);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('dappier-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((searchQuery: string) => {
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('dappier-recent-searches', JSON.stringify(updated));
  }, [recentSearches]);

  // Debounced search function
  const debouncedSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setSuggestions([]);
      setShowResults(false);
      return;
    }

    try {
      const response = await search(searchQuery, {
        pagination: { page: 1, limit: maxResults },
        includeSnippets: true,
        includeMetadata: true,
        filters,
      });

      setResults(response.results);
      setSuggestions(response.suggestions || []);
      setShowResults(true);
      
      // Call onSearch callback
      if (onSearch) {
        onSearch(searchQuery, response.results);
      }
    } catch (err) {
      console.error('Search error:', err);
      setResults([]);
      setSuggestions([]);
    }
  }, [search, maxResults, filters, onSearch]);

  // Handle input change with debouncing
  const handleInputChange = useCallback((value: string) => {
    setQuery(value);
    
    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    // Debounce search
    debounceRef.current = setTimeout(() => {
      debouncedSearch(value);
    }, 300);
  }, [debouncedSearch]);

  // Handle search submission
  const handleSearch = useCallback(async () => {
    if (!query.trim()) return;
    
    saveRecentSearch(query);
    await debouncedSearch(query);
  }, [query, saveRecentSearch, debouncedSearch]);

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    setShowResults(false);
    setQuery('');
    
    if (onResultSelect) {
      onResultSelect(result);
    }
  }, [onResultSelect]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: string) => {
    setQuery(suggestion);
    handleInputChange(suggestion);
  }, [handleInputChange]);

  // Handle recent search selection
  const handleRecentSearchSelect = useCallback((recentQuery: string) => {
    setQuery(recentQuery);
    handleInputChange(recentQuery);
  }, [handleInputChange]);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setSuggestions([]);
    setShowResults(false);
    inputRef.current?.focus();
  }, []);

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSearch();
    } else if (event.key === 'Escape') {
      setShowResults(false);
      inputRef.current?.blur();
    }
  }, [handleSearch]);

  return (
    <div ref={searchRef} className={`relative w-full max-w-2xl ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          ) : (
            <Search className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
        
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowResults(true)}
          className="pl-10 pr-20 h-12 text-base"
          disabled={isLoading}
        />
        
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {query && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          
          <Button
            onClick={handleSearch}
            disabled={!query.trim() || isLoading}
            className="h-8 px-3"
          >
            <Sparkles className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-50 max-h-96 overflow-hidden shadow-lg">
          <CardContent className="p-0">
            {/* Error State */}
            {error && (
              <div className="p-4 text-center text-destructive">
                <p className="text-sm">Search failed: {error.message}</p>
              </div>
            )}

            {/* No Query State */}
            {!query.trim() && !isLoading && (
              <div className="p-4">
                {recentSearches.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      Recent Searches
                    </div>
                    <div className="space-y-1">
                      {recentSearches.map((recentQuery, index) => (
                        <button
                          key={index}
                          onClick={() => handleRecentSearchSelect(recentQuery)}
                          className="w-full text-left px-3 py-2 rounded-md hover:bg-muted transition-colors text-sm"
                        >
                          {recentQuery}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Searching with AI...</span>
                </div>
              </div>
            )}

            {/* Results */}
            {!isLoading && query.trim() && (
              <div className="max-h-80 overflow-y-auto">
                {/* Suggestions */}
                {showSuggestions && suggestions.length > 0 && (
                  <div className="p-4 border-b">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                      <TrendingUp className="h-4 w-4" />
                      Suggestions
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {suggestions.map((suggestion, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                          onClick={() => handleSuggestionSelect(suggestion)}
                        >
                          {suggestion}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Search Results */}
                {results.length > 0 ? (
                  <div className="divide-y">
                    {results.map((result, index) => (
                      <button
                        key={result.id || index}
                        onClick={() => handleResultSelect(result)}
                        className="w-full text-left p-4 hover:bg-muted transition-colors group"
                      >
                        <div className="space-y-2">
                          <div className="flex items-start justify-between">
                            <h3 className="font-medium text-sm group-hover:text-primary transition-colors line-clamp-1">
                              {result.title}
                            </h3>
                            <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0 ml-2" />
                          </div>
                          
                          {result.snippet && (
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {result.snippet}
                            </p>
                          )}
                          
                          <div className="flex items-center gap-2">
                            {result.score && (
                              <Badge variant="outline" className="text-xs">
                                {Math.round(result.score * 100)}% match
                              </Badge>
                            )}
                            
                            {result.source && (
                              <Badge variant="secondary" className="text-xs">
                                {result.source.name}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : query.trim() && !isLoading ? (
                  <div className="p-4 text-center text-muted-foreground">
                    <p className="text-sm">No results found for "{query}"</p>
                    <p className="text-xs mt-1">Try different keywords or check your spelling</p>
                  </div>
                ) : null}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
