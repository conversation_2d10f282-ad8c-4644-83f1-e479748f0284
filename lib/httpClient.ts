/**
 * HTTP Client for API requests with interceptors and error handling
 * Supports authentication, retry logic, and standardized error responses
 */

import { ApiResponse } from './types/revenuecat';

export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: unknown;
  headers?: Record<string, string>;
  params?: Record<string, string>;
  timeout?: number;
}

export class HttpClient {
  private config: HttpClientConfig;
  private defaultHeaders: Record<string, string>;

  constructor(config: HttpClientConfig) {
    this.config = {
      timeout: 10000,
      retries: 3,
      retryDelay: 1000,
      ...config,
    };

    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers,
    };
  }

  /**
   * Set authentication header
   */
  setAuthHeader(token: string, type: 'Bearer' | 'Basic' = 'Bearer'): void {
    this.defaultHeaders['Authorization'] = `${type} ${token}`;
  }

  /**
   * Remove authentication header
   */
  removeAuthHeader(): void {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Make HTTP request with retry logic
   */
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const { method, url, data, headers = {}, params, timeout } = config;
    
    const fullUrl = this.buildUrl(url, params);
    const requestHeaders = { ...this.defaultHeaders, ...headers };
    const requestTimeout = timeout || this.config.timeout;

    let lastError: Error | null = null;
    const maxRetries = this.config.retries || 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), requestTimeout);

        const response = await fetch(fullUrl, {
          method,
          headers: requestHeaders,
          body: data ? JSON.stringify(data) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const responseData = await this.handleResponse<T>(response);
        return responseData;

      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on client errors (4xx) or if it's the last attempt
        if (attempt === maxRetries || this.isClientError(error)) {
          break;
        }

        // Wait before retrying
        if (attempt < maxRetries) {
          await this.delay(this.config.retryDelay! * Math.pow(2, attempt));
        }
      }
    }

    // Return error response
    return {
      success: false,
      error: {
        code: 0,
        message: lastError?.message || 'Unknown error occurred',
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * GET request
   */
  async get<T>(url: string, params?: Record<string, string>, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'GET', url, params, headers });
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: unknown, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'POST', url, data, headers });
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: unknown, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PUT', url, data, headers });
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'DELETE', url, headers });
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: unknown, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PATCH', url, data, headers });
  }

  /**
   * Build full URL with query parameters
   */
  private buildUrl(url: string, params?: Record<string, string>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;
    
    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }

    const urlObj = new URL(fullUrl);
    Object.entries(params).forEach(([key, value]) => {
      urlObj.searchParams.append(key, value);
    });

    return urlObj.toString();
  }

  /**
   * Handle fetch response
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const timestamp = new Date().toISOString();

    try {
      const responseText = await response.text();
      let responseData: unknown;

      try {
        responseData = responseText ? JSON.parse(responseText) : null;
      } catch {
        responseData = responseText;
      }

      if (!response.ok) {
        const errorData = responseData as { message?: string; attribute_errors?: Record<string, string[]> };
        return {
          success: false,
          error: {
            code: response.status,
            message: errorData?.message || response.statusText || 'Request failed',
            attribute_errors: errorData?.attribute_errors,
          },
          timestamp,
        };
      }

      return {
        success: true,
        data: responseData as T,
        timestamp,
      };

    } catch (error) {
      return {
        success: false,
        error: {
          code: response.status || 0,
          message: (error as Error).message || 'Failed to parse response',
        },
        timestamp,
      };
    }
  }

  /**
   * Check if error is a client error (4xx)
   */
  private isClientError(error: unknown): boolean {
    const errorObj = error as { name?: string; status?: number };
    if (errorObj?.name === 'AbortError') return false;
    if (errorObj?.status && errorObj.status >= 400 && errorObj.status < 500) return true;
    return false;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update base configuration
   */
  updateConfig(newConfig: Partial<HttpClientConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (newConfig.headers) {
      this.defaultHeaders = { ...this.defaultHeaders, ...newConfig.headers };
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): HttpClientConfig {
    return { ...this.config };
  }
}

/**
 * Create a pre-configured HTTP client instance
 */
export function createHttpClient(config: HttpClientConfig): HttpClient {
  return new HttpClient(config);
}

/**
 * Default HTTP client factory with common settings
 */
export function createDefaultHttpClient(baseURL: string, apiKey?: string): HttpClient {
  const client = new HttpClient({
    baseURL,
    timeout: 15000,
    retries: 3,
    retryDelay: 1000,
    headers: {
      'User-Agent': 'NextJS-App/1.0',
    },
  });

  if (apiKey) {
    client.setAuthHeader(apiKey);
  }

  return client;
}
