/**
 * Dappier Custom Copilot Chat Component
 * Intelligent chatbot interface with context awareness
 */

'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useDappierCopilot } from '@/hooks/useDappier';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Send, 
  Loader2, 
  Bot, 
  User, 
  Trash2, 
  Copy,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  MessageSquare,
  Clock
} from 'lucide-react';
import type { CopilotPersonality, CopilotCapability } from '@/lib/types/dappier';

interface CopilotChatProps {
  title?: string;
  personality?: CopilotPersonality;
  capabilities?: CopilotCapability[];
  placeholder?: string;
  maxHeight?: string;
  showHeader?: boolean;
  showActions?: boolean;
  onMessageSent?: (message: string) => void;
  onResponseReceived?: (response: string) => void;
  className?: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  actions?: Array<{ type: string; label: string; data: any }>;
  sources?: Array<{ name: string; url?: string }>;
  confidence?: number;
}

export function CopilotChat({
  title = "AI Assistant",
  personality = {
    tone: 'friendly',
    responseStyle: 'conversational',
  },
  capabilities = [],
  placeholder = "Ask me anything...",
  maxHeight = "400px",
  showHeader = true,
  showActions = true,
  onMessageSent,
  onResponseReceived,
  className = "",
}: CopilotChatProps) {
  const { chat, messages: hookMessages, conversationId, clearConversation, isLoading, error } = useDappierCopilot();
  
  const [inputMessage, setInputMessage] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Sync hook messages with local state
  useEffect(() => {
    const transformedMessages: ChatMessage[] = hookMessages.map((msg, index) => ({
      id: `msg_${index}_${Date.now()}`,
      role: msg.role,
      content: msg.content,
      timestamp: msg.timestamp,
    }));
    setChatMessages(transformedMessages);
  }, [hookMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, isLoading]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle message submission
  const handleSendMessage = useCallback(async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setIsTyping(true);

    // Call onMessageSent callback
    if (onMessageSent) {
      onMessageSent(userMessage);
    }

    try {
      const response = await chat(userMessage, {
        personality,
        capabilities,
        context: {
          currentPage: window.location.pathname,
          sessionData: {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
          },
        },
      });

      // Call onResponseReceived callback
      if (onResponseReceived) {
        onResponseReceived(response.message);
      }
    } catch (err) {
      console.error('Chat error:', err);
    } finally {
      setIsTyping(false);
    }
  }, [inputMessage, isLoading, chat, personality, capabilities, onMessageSent, onResponseReceived]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Copy message to clipboard
  const copyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy message:', err);
    }
  }, []);

  // Clear conversation
  const handleClearConversation = useCallback(() => {
    clearConversation();
    setChatMessages([]);
    inputRef.current?.focus();
  }, [clearConversation]);

  // Format timestamp
  const formatTimestamp = useCallback((timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }, []);

  return (
    <Card className={`flex flex-col ${className}`} style={{ height: maxHeight }}>
      {/* Header */}
      {showHeader && (
        <CardHeader className="flex-shrink-0 pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-2 rounded-full bg-primary/10">
                <Bot className="h-4 w-4 text-primary" />
              </div>
              {title}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {conversationId && (
                <Badge variant="outline" className="text-xs">
                  {conversationId.slice(-8)}
                </Badge>
              )}
              
              {showActions && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearConversation}
                  className="h-8 w-8 p-0"
                  title="Clear conversation"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          
          {/* Personality and capabilities info */}
          {(personality || capabilities.length > 0) && (
            <div className="flex flex-wrap gap-2 mt-2">
              {personality && (
                <Badge variant="secondary" className="text-xs">
                  {personality.tone} • {personality.responseStyle}
                </Badge>
              )}
              {capabilities.map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability.name}
                </Badge>
              ))}
            </div>
          )}
        </CardHeader>
      )}

      {/* Messages */}
      <CardContent className="flex-1 flex flex-col min-h-0 p-4">
        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-4">
            {/* Welcome message */}
            {chatMessages.length === 0 && !isLoading && (
              <div className="text-center py-8">
                <div className="p-3 rounded-full bg-muted w-fit mx-auto mb-3">
                  <Sparkles className="h-6 w-6 text-muted-foreground" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Hi! I'm your AI assistant. How can I help you today?
                </p>
              </div>
            )}

            {/* Chat messages */}
            {chatMessages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-primary/10">
                      <Bot className="h-4 w-4 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                )}

                <div
                  className={`max-w-[80%] space-y-2 ${
                    message.role === 'user' ? 'order-1' : 'order-2'
                  }`}
                >
                  <div
                    className={`rounded-lg px-3 py-2 text-sm ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground ml-auto'
                        : 'bg-muted'
                    }`}
                  >
                    <p className="whitespace-pre-wrap">{message.content}</p>
                  </div>

                  {/* Message metadata */}
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{formatTimestamp(message.timestamp)}</span>
                    
                    {message.confidence && (
                      <Badge variant="outline" className="text-xs">
                        {Math.round(message.confidence * 100)}% confident
                      </Badge>
                    )}

                    {showActions && (
                      <div className="flex items-center gap-1 ml-auto">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyMessage(message.content)}
                          className="h-6 w-6 p-0"
                          title="Copy message"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        
                        {message.role === 'assistant' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              title="Good response"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              title="Poor response"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Sources */}
                  {message.sources && message.sources.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {message.sources.map((source, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {source.name}
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* Actions */}
                  {message.actions && message.actions.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {message.actions.map((action, index) => (
                        <Button key={index} variant="outline" size="sm" className="h-7 text-xs">
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-primary/10">
                      <User className="h-4 w-4 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {/* Typing indicator */}
            {(isLoading || isTyping) && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarFallback className="bg-primary/10">
                    <Bot className="h-4 w-4 text-primary" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-3 py-2">
                  <div className="flex items-center gap-1">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                    </div>
                    <span className="text-xs text-muted-foreground ml-2">Thinking...</span>
                  </div>
                </div>
              </div>
            )}

            {/* Error message */}
            {error && (
              <div className="text-center py-4">
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="flex-shrink-0 pt-4">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              className="px-3"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
