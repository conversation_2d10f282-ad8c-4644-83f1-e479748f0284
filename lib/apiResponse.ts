/**
 * Standardized API Response Wrapper
 * Provides consistent error handling and response formatting across all API routes
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';

export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
}

export interface ApiSuccessResponse<T = unknown> {
  success: true;
  data: T;
  timestamp: string;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
  };
}

export interface ApiErrorResponse {
  success: false;
  error: ApiError;
}

export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(
  data: T,
  meta?: ApiSuccessResponse<T>['meta']
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    ...(meta && { meta }),
  };

  return NextResponse.json(response, { status: 200 });
}

/**
 * Create an error API response
 */
export function createErrorResponse(
  error: string | ApiError,
  status: number = 500
): NextResponse<ApiErrorResponse> {
  const apiError: ApiError = typeof error === 'string' 
    ? {
        code: 'INTERNAL_ERROR',
        message: error,
        timestamp: new Date().toISOString(),
      }
    : {
        ...error,
        timestamp: error.timestamp || new Date().toISOString(),
      };

  const response: ApiErrorResponse = {
    success: false,
    error: apiError,
  };

  return NextResponse.json(response, { status });
}

/**
 * Handle and format different types of errors
 */
export function handleApiError(error: unknown): NextResponse<ApiErrorResponse> {
  console.error('API Error:', error);

  // Zod validation errors
  if (error instanceof ZodError) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid request data',
      details: error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      })),
      timestamp: new Date().toISOString(),
    }, 400);
  }

  // Network/Fetch errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return createErrorResponse({
      code: 'NETWORK_ERROR',
      message: 'Failed to connect to external service',
      timestamp: new Date().toISOString(),
    }, 503);
  }

  // Custom API errors
  if (error && typeof error === 'object' && 'code' in error && 'message' in error) {
    return createErrorResponse(error as ApiError, 400);
  }

  // HTTP errors with status codes
  if (error && typeof error === 'object' && 'status' in error) {
    const httpError = error as { status: number; message?: string };
    return createErrorResponse({
      code: `HTTP_${httpError.status}`,
      message: httpError.message || `HTTP Error ${httpError.status}`,
      timestamp: new Date().toISOString(),
    }, httpError.status);
  }

  // Generic errors
  const message = error instanceof Error ? error.message : 'An unexpected error occurred';
  return createErrorResponse({
    code: 'INTERNAL_ERROR',
    message,
    timestamp: new Date().toISOString(),
  }, 500);
}

/**
 * Validate request method
 */
export function validateMethod(
  request: Request,
  allowedMethods: string[]
): NextResponse<ApiErrorResponse> | null {
  if (!allowedMethods.includes(request.method)) {
    return createErrorResponse({
      code: 'METHOD_NOT_ALLOWED',
      message: `Method ${request.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
      timestamp: new Date().toISOString(),
    }, 405);
  }
  return null;
}

/**
 * Validate required environment variables
 */
export function validateEnvVars(vars: Record<string, string | undefined>): string[] {
  const missing: string[] = [];
  
  Object.entries(vars).forEach(([key, value]) => {
    if (!value) {
      missing.push(key);
    }
  });

  return missing;
}

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): NextResponse<ApiSuccessResponse<T[]>> {
  const hasMore = page * limit < total;
  
  return createSuccessResponse(data, {
    page,
    limit,
    total,
    hasMore,
  });
}

/**
 * Extract and validate JSON body from request
 */
export async function extractJsonBody<T>(request: Request): Promise<T> {
  try {
    const body = await request.json();
    return body as T;
  } catch {
    throw new Error('Invalid JSON in request body');
  }
}

/**
 * Extract query parameters from URL
 */
export function extractQueryParams(url: string): Record<string, string> {
  const urlObj = new URL(url);
  const params: Record<string, string> = {};
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

/**
 * Rate limiting helper (basic implementation)
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 60000 // 1 minute
  ) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || [];
    
    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);
    
    // Check if under limit
    if (recentRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    return true;
  }

  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    const requests = this.requests.get(identifier) || [];
    const recentRequests = requests.filter(time => time > windowStart);
    
    return Math.max(0, this.maxRequests - recentRequests.length);
  }
}

/**
 * CORS headers for API routes
 */
export function addCorsHeaders(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Max-Age', '86400');
  
  return response;
}

/**
 * Handle OPTIONS requests for CORS
 */
export function handleOptionsRequest(): NextResponse {
  const response = new NextResponse(null, { status: 200 });
  return addCorsHeaders(response);
}

/**
 * Async wrapper for API route handlers with error handling
 */
export function withErrorHandling<T extends unknown[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ApiErrorResponse>> => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

/**
 * Middleware for API authentication
 */
export function requireAuth(request: Request): string | NextResponse<ApiErrorResponse> {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader) {
    return createErrorResponse({
      code: 'MISSING_AUTH',
      message: 'Authorization header is required',
      timestamp: new Date().toISOString(),
    }, 401);
  }

  const token = authHeader.replace('Bearer ', '');
  if (!token) {
    return createErrorResponse({
      code: 'INVALID_AUTH',
      message: 'Invalid authorization format. Use: Bearer <token>',
      timestamp: new Date().toISOString(),
    }, 401);
  }

  return token;
}
