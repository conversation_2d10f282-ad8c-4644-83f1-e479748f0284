/**
 * RevenueCat API Route Handler
 * Handles server-side RevenueCat operations using REST API v1
 * Documentation: https://www.revenuecat.com/docs/api-v1
 */

import { NextRequest } from 'next/server';
import { z } from 'zod';
import {
  createSuccessResponse,
  createErrorResponse,
  validateMethod,
  validateEnvVars,
  extractJsonBody,
  extractQueryParams,
  withErrorHandling,
} from '@/lib/apiResponse';
import { createDefaultHttpClient, HttpClient } from '@/lib/httpClient';
import {
  Subscriber,
  OfferingsResponse,
  RevenueCatResponse,
} from '@/lib/types/revenuecat';

// Environment validation
const requiredEnvVars = {
  REVENUECAT_SECRET_KEY: process.env.REVENUECAT_SECRET_KEY,
  REVENUECAT_PUBLIC_KEY: process.env.REVENUECAT_PUBLIC_KEY,
};

// Validation schemas
const getCustomerInfoSchema = z.object({
  app_user_id: z.string().min(1, 'App user ID is required'),
});

const getOfferingsSchema = z.object({
  app_user_id: z.string().min(1, 'App user ID is required'),
});

const addAttributionSchema = z.object({
  app_user_id: z.string().min(1, 'App user ID is required'),
  attribution_data: z.object({
    data: z.record(z.any()),
    from: z.enum(['APPLE_SEARCH_ADS', 'ADJUST', 'APPSFLYER', 'BRANCH', 'TENJIN', 'FACEBOOK', 'MPARTICLE']),
    network_user_id: z.string().optional(),
  }),
});

const processReceiptSchema = z.object({
  app_user_id: z.string().min(1, 'App user ID is required'),
  fetch_token: z.string().min(1, 'Fetch token is required'),
  attributes: z.record(z.string()).optional(),
  normal_duration: z.string().optional(),
  intro_duration: z.string().optional(),
  trial_duration: z.string().optional(),
});

// RevenueCat API client
const REVENUECAT_BASE_URL = 'https://api.revenuecat.com/v1';

function createRevenueCatClient() {
  const missing = validateEnvVars(requiredEnvVars);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  const client = createDefaultHttpClient(REVENUECAT_BASE_URL);
  client.setAuthHeader(requiredEnvVars.REVENUECAT_SECRET_KEY!);
  
  return client;
}

/**
 * GET /api/revenuecat
 * Handles multiple GET operations based on query parameters
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  const methodError = validateMethod(request, ['GET']);
  if (methodError) return methodError;

  const params = extractQueryParams(request.url);
  const { action, app_user_id } = params;

  if (!action) {
    return createErrorResponse({
      code: 'MISSING_ACTION',
      message: 'Action parameter is required. Supported actions: customer-info, offerings',
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const client = createRevenueCatClient();

  switch (action) {
    case 'customer-info':
      return await getCustomerInfo(client, { app_user_id });
    
    case 'offerings':
      return await getOfferings(client, { app_user_id });
    
    default:
      return createErrorResponse({
        code: 'INVALID_ACTION',
        message: `Unsupported action: ${action}. Supported actions: customer-info, offerings`,
        timestamp: new Date().toISOString(),
      }, 400);
  }
});

/**
 * POST /api/revenuecat
 * Handles POST operations based on request body action
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  const methodError = validateMethod(request, ['POST']);
  if (methodError) return methodError;

  const body = await extractJsonBody<{ action?: string }>(request);
  const { action } = body;

  if (!action) {
    return createErrorResponse({
      code: 'MISSING_ACTION',
      message: 'Action field is required in request body. Supported actions: add-attribution, process-receipt',
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const client = createRevenueCatClient();

  switch (action) {
    case 'add-attribution':
      return await addAttribution(client, body);
    
    case 'process-receipt':
      return await processReceipt(client, body);
    
    default:
      return createErrorResponse({
        code: 'INVALID_ACTION',
        message: `Unsupported action: ${action}. Supported actions: add-attribution, process-receipt`,
        timestamp: new Date().toISOString(),
      }, 400);
  }
});

/**
 * Get customer information
 */
async function getCustomerInfo(client: HttpClient, params: unknown) {
  const validation = getCustomerInfoSchema.safeParse(params);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid parameters',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const { app_user_id } = validation.data;
  
  const response = await client.get<RevenueCatResponse<Subscriber>>(
    `/subscribers/${encodeURIComponent(app_user_id)}`
  );

  if (!response.success) {
    return createErrorResponse({
      code: 'REVENUECAT_ERROR',
      message: response.error?.message || 'Failed to fetch customer info',
      details: response.error,
      timestamp: new Date().toISOString(),
    }, response.error?.code === 404 ? 404 : 500);
  }

  return createSuccessResponse(response.data?.subscriber);
}

/**
 * Get available offerings
 */
async function getOfferings(client: HttpClient, params: unknown) {
  const validation = getOfferingsSchema.safeParse(params);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid parameters',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const { app_user_id } = validation.data;
  
  const response = await client.get<OfferingsResponse>(
    `/subscribers/${encodeURIComponent(app_user_id)}/offerings`
  );

  if (!response.success) {
    return createErrorResponse({
      code: 'REVENUECAT_ERROR',
      message: response.error?.message || 'Failed to fetch offerings',
      details: response.error,
      timestamp: new Date().toISOString(),
    }, 500);
  }

  return createSuccessResponse(response.data);
}

/**
 * Add attribution data
 */
async function addAttribution(client: HttpClient, body: unknown) {
  const validation = addAttributionSchema.safeParse(body);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid request body',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const { app_user_id, attribution_data } = validation.data;
  
  const response = await client.post(
    `/subscribers/${encodeURIComponent(app_user_id)}/attribution`,
    attribution_data
  );

  if (!response.success) {
    return createErrorResponse({
      code: 'REVENUECAT_ERROR',
      message: response.error?.message || 'Failed to add attribution data',
      details: response.error,
      timestamp: new Date().toISOString(),
    }, 500);
  }

  return createSuccessResponse({ success: true });
}

/**
 * Process receipt
 */
async function processReceipt(client: HttpClient, body: unknown) {
  const validation = processReceiptSchema.safeParse(body);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid request body',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const receiptData = validation.data;
  
  const response = await client.post('/receipts', receiptData);

  if (!response.success) {
    return createErrorResponse({
      code: 'REVENUECAT_ERROR',
      message: response.error?.message || 'Failed to process receipt',
      details: response.error,
      timestamp: new Date().toISOString(),
    }, 500);
  }

  return createSuccessResponse(response.data);
}
