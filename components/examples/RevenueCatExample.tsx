/**
 * RevenueCat Integration Example Component
 * Demonstrates how to use the RevenueCat hooks and API integration
 */

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, RefreshCw, User, CreditCard, Gift } from 'lucide-react';
import {
  useRevenueCat,
  useSubscriptionStatus,
  useRevenueCatAttribution,
} from '@/hooks/useRevenueCat';

export function RevenueCatExample() {
  const [userId, setUserId] = useState('demo_user_123');
  const [isEnabled, setIsEnabled] = useState(false);

  // Main RevenueCat hook
  const {
    customerInfo,
    offerings,
    isLoading: isMainLoading,
    error: mainError,
    refreshCustomerInfo,
    refreshOfferings,
  } = useRevenueCat({
    userId: isEnabled ? userId : undefined,
    enableLogging: true,
  });

  // Subscription status hook
  const {
    isSubscribed,
    activeSubscriptions,
    isLoading: isStatusLoading,
    error: statusError,
  } = useSubscriptionStatus(isEnabled ? userId : undefined);

  // Attribution hook
  const {
    addAttribution,
    isLoading: isAttributionLoading,
    error: attributionError,
  } = useRevenueCatAttribution();

  const handleConnect = () => {
    if (!userId.trim()) {
      alert('Please enter a user ID');
      return;
    }
    setIsEnabled(true);
  };

  const handleDisconnect = () => {
    setIsEnabled(false);
  };

  const handleAddAttribution = async () => {
    try {
      await addAttribution(userId, {
        data: {
          campaign: 'demo_campaign',
          source: 'example_component',
          medium: 'web',
        },
        from: 'FACEBOOK',
        network_user_id: 'demo_network_user',
      });
      alert('Attribution added successfully!');
    } catch (error) {
      console.error('Failed to add attribution:', error);
    }
  };

  const isLoading = isMainLoading || isStatusLoading;
  const error = mainError || statusError || attributionError;

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">RevenueCat Integration Example</h1>
        <p className="text-muted-foreground">
          Demonstrates the RevenueCat integration with real API calls
        </p>
      </div>

      {/* Connection Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Connection Settings
          </CardTitle>
          <CardDescription>
            Enter a user ID to test the RevenueCat integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Enter user ID (e.g., demo_user_123)"
                disabled={isEnabled}
              />
            </div>
            <div>
              {!isEnabled ? (
                <Button onClick={handleConnect} disabled={!userId.trim()}>
                  Connect
                </Button>
              ) : (
                <Button onClick={handleDisconnect} variant="outline">
                  Disconnect
                </Button>
              )}
            </div>
          </div>

          {isEnabled && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Connected as: {userId}</Badge>
              {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            <strong>Error:</strong> {error.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Customer Information */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshCustomerInfo}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading customer info...</span>
              </div>
            ) : customerInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>App User ID:</strong> {customerInfo.app_user_id}
                  </div>
                  <div>
                    <strong>Original App User ID:</strong> {customerInfo.original_app_user_id}
                  </div>
                  <div>
                    <strong>First Seen:</strong> {new Date(customerInfo.first_seen).toLocaleDateString()}
                  </div>
                  <div>
                    <strong>Last Seen:</strong> {new Date(customerInfo.last_seen).toLocaleDateString()}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-semibold mb-2">Entitlements</h4>
                  {Object.keys(customerInfo.entitlements).length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(customerInfo.entitlements).map(([key, entitlement]) => (
                        <div key={key} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{key}</span>
                            <Badge variant="secondary">
                              {entitlement.product_identifier}
                            </Badge>
                          </div>
                          {entitlement.expires_date && (
                            <p className="text-sm text-muted-foreground">
                              Expires: {new Date(entitlement.expires_date).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No active entitlements</p>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-semibold mb-2">Subscriptions</h4>
                  {Object.keys(customerInfo.subscriptions).length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(customerInfo.subscriptions).map(([key, subscription]) => (
                        <div key={key} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{subscription.product_identifier}</span>
                            <div className="flex gap-2">
                              <Badge variant={subscription.is_sandbox ? "outline" : "default"}>
                                {subscription.is_sandbox ? 'Sandbox' : 'Production'}
                              </Badge>
                              <Badge variant="secondary">
                                {subscription.store}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground mt-1">
                            <p>Expires: {new Date(subscription.expires_date).toLocaleDateString()}</p>
                            <p>Period: {subscription.period_type}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No active subscriptions</p>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No customer information available</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Subscription Status */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Subscription Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isStatusLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Checking status...</span>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span>Status:</span>
                  <Badge variant={isSubscribed ? "default" : "secondary"}>
                    {isSubscribed ? 'Subscribed' : 'Not Subscribed'}
                  </Badge>
                </div>

                {activeSubscriptions.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Active Subscriptions</h4>
                    <div className="space-y-2">
                      {activeSubscriptions.map(([key, subscription]) => (
                        <div key={key} className="p-2 border rounded">
                          <span className="font-medium">{subscription.product_identifier}</span>
                          <p className="text-sm text-muted-foreground">
                            Expires: {new Date(subscription.expires_date).toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Offerings */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Available Offerings
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshOfferings}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading offerings...</span>
              </div>
            ) : offerings && offerings.length > 0 ? (
              <div className="space-y-4">
                {offerings.map((offering) => (
                  <div key={offering.identifier} className="p-4 border rounded-lg">
                    <h4 className="font-semibold">{offering.description}</h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      ID: {offering.identifier}
                    </p>
                    
                    <div className="space-y-2">
                      <h5 className="font-medium">Packages:</h5>
                      {offering.packages.map((pkg) => (
                        <div key={pkg.identifier} className="flex justify-between items-center p-2 bg-muted rounded">
                          <div>
                            <span className="font-medium">{pkg.identifier}</span>
                            <p className="text-sm text-muted-foreground">
                              {pkg.platform_product_identifier}
                            </p>
                          </div>
                          <Button size="sm" disabled>
                            Subscribe
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No offerings available</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Attribution */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle>Attribution Testing</CardTitle>
            <CardDescription>
              Test adding attribution data for marketing tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleAddAttribution}
              disabled={isAttributionLoading}
            >
              {isAttributionLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Adding Attribution...
                </>
              ) : (
                'Add Demo Attribution'
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
