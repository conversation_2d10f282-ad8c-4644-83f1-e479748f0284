# RevenueCat Integration Setup Guide

This guide covers the complete setup and usage of the RevenueCat integration in your Next.js 14 App Router application.

## Overview

RevenueCat is a subscription management platform that simplifies in-app purchases and subscription handling. This integration provides:

- **Client-side JavaScript SDK** for primary operations (getCustomerInfo, getOfferings, purchase)
- **Server-side API routes** as fallback for operations not supported by the SDK
- **React hooks** that prioritize SDK usage with REST API fallback
- **TypeScript support** with comprehensive type definitions
- **Error handling** and retry logic
- **Real-time subscription management** through the SDK

## Prerequisites

1. **RevenueCat Account**: Sign up at [app.revenuecat.com](https://app.revenuecat.com)
2. **Payment Provider**: Connect Stripe or other supported payment providers
3. **Products & Offerings**: Set up your subscription products and offerings

## Installation

The RevenueCat dependencies are already included in the project:

```bash
pnpm install
```

## Environment Configuration

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Configure your RevenueCat credentials in `.env.local`:

```env
# RevenueCat Configuration (SDK-first approach)
# Primary: Public key for client-side SDK operations
NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY=pk_test_your_public_key_here
NEXT_PUBLIC_REVENUECAT_ENVIRONMENT=sandbox

# Fallback: Secret key for server-side operations only
REVENUECAT_SECRET_KEY=sk_test_your_secret_key_here
REVENUECAT_APP_ID=your_app_id_here
```

### Getting Your API Keys

1. Go to [RevenueCat Dashboard](https://app.revenuecat.com)
2. Navigate to **Project Settings** → **API Keys**
3. Copy your **Public Key** (for client-side SDK operations) - **PRIMARY**
4. Copy your **Secret Key** (for server-side operations only) - **FALLBACK**

## API Routes

### Available Endpoints

#### GET `/api/revenuecat`

Get customer information or offerings:

```typescript
// Get customer info
GET /api/revenuecat?action=customer-info&app_user_id=user123

// Get offerings
GET /api/revenuecat?action=offerings&app_user_id=user123
```

#### POST `/api/revenuecat`

Perform actions like adding attribution or processing receipts:

```typescript
// Add attribution data
POST /api/revenuecat
{
  "action": "add-attribution",
  "app_user_id": "user123",
  "attribution_data": {
    "data": { "campaign": "summer_sale" },
    "from": "FACEBOOK"
  }
}

// Process receipt
POST /api/revenuecat
{
  "action": "process-receipt",
  "app_user_id": "user123",
  "fetch_token": "receipt_token_here"
}
```

## Client-Side Usage (SDK-First)

### Basic Hook Usage with JavaScript SDK

```typescript
import { useRevenueCat } from '@/hooks/useRevenueCat';

function SubscriptionComponent() {
  const {
    customerInfo,
    offerings,
    isLoading,
    error,
    refreshCustomerInfo,
    refreshOfferings,
    purchasePackage,
  } = useRevenueCat({
    userId: 'user123',
    enableLogging: true,
    // apiKey is optional - will use NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY by default
  });

  const handlePurchase = async (packageId: string) => {
    try {
      await purchasePackage(packageId);
      console.log('Purchase successful!');
    } catch (err) {
      console.error('Purchase failed:', err);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h2>Customer Info (from SDK)</h2>
      <pre>{JSON.stringify(customerInfo, null, 2)}</pre>

      <h2>Available Offerings (from SDK)</h2>
      {offerings?.map(offering => (
        <div key={offering.identifier}>
          <h3>{offering.description}</h3>
          {offering.packages.map(pkg => (
            <button
              key={pkg.identifier}
              onClick={() => handlePurchase(pkg.identifier)}
            >
              Subscribe to {pkg.platform_product_identifier}
            </button>
          ))}
        </div>
      ))}
    </div>
  );
}
```

### Subscription Status Hook

```typescript
import { useSubscriptionStatus } from '@/hooks/useRevenueCat';

function SubscriptionStatus({ userId }: { userId: string }) {
  const {
    isSubscribed,
    activeEntitlements,
    activeSubscriptions,
    isLoading,
    error,
  } = useSubscriptionStatus(userId);

  if (isLoading) return <div>Checking subscription...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <p>Subscription Status: {isSubscribed ? 'Active' : 'Inactive'}</p>
      
      {Object.entries(activeEntitlements).map(([key, entitlement]) => (
        <div key={key}>
          <p>Entitlement: {key}</p>
          <p>Product: {entitlement.product_identifier}</p>
          <p>Expires: {entitlement.expires_date || 'Never'}</p>
        </div>
      ))}
    </div>
  );
}
```

### Attribution Hook

```typescript
import { useRevenueCatAttribution } from '@/hooks/useRevenueCat';

function AttributionComponent() {
  const { addAttribution, isLoading, error } = useRevenueCatAttribution();

  const handleAddAttribution = async () => {
    try {
      await addAttribution('user123', {
        data: { campaign: 'facebook_ads', ad_group: 'premium_users' },
        from: 'FACEBOOK',
        network_user_id: 'fb_user_456',
      });
      console.log('Attribution added successfully');
    } catch (err) {
      console.error('Failed to add attribution:', err);
    }
  };

  return (
    <button onClick={handleAddAttribution} disabled={isLoading}>
      {isLoading ? 'Adding...' : 'Add Attribution'}
    </button>
  );
}
```

## Type Definitions

The integration includes comprehensive TypeScript types:

```typescript
import {
  Subscriber,
  Offering,
  Package,
  Entitlement,
  Subscription,
  AttributionData,
  RevenueCatError,
} from '@/lib/types/revenuecat';
```

### Key Types

- **Subscriber**: Complete customer information including subscriptions and entitlements
- **Offering**: Available subscription offerings with packages
- **Package**: Individual subscription packages within offerings
- **Entitlement**: Customer's access rights to features
- **Subscription**: Active subscription details
- **AttributionData**: Marketing attribution information

## Error Handling

The integration provides comprehensive error handling:

```typescript
import { RevenueCatErrorCode } from '@/lib/types/revenuecat';

function handleRevenueCatError(error: RevenueCatError) {
  switch (error.code) {
    case RevenueCatErrorCode.NETWORK_ERROR:
      // Handle network issues
      break;
    case RevenueCatErrorCode.INVALID_CREDENTIALS:
      // Handle authentication issues
      break;
    case RevenueCatErrorCode.PRODUCT_NOT_AVAILABLE:
      // Handle product availability issues
      break;
    default:
      // Handle other errors
      console.error('RevenueCat error:', error.message);
  }
}
```

## Testing

### Development Testing

1. Use sandbox environment for testing:
```env
REVENUECAT_ENVIRONMENT=sandbox
```

2. Create test users in RevenueCat dashboard
3. Use test payment methods in your payment provider

### API Testing

Test the API routes using curl or your preferred HTTP client:

```bash
# Test customer info endpoint
curl -X GET "http://localhost:3000/api/revenuecat?action=customer-info&app_user_id=test_user"

# Test offerings endpoint
curl -X GET "http://localhost:3000/api/revenuecat?action=offerings&app_user_id=test_user"
```

## Production Deployment

1. **Environment Variables**: Set production environment variables in your hosting platform
2. **API Keys**: Use production API keys from RevenueCat
3. **Payment Provider**: Configure production payment provider settings
4. **Monitoring**: Enable error monitoring and logging

### Production Checklist

- [ ] Production API keys configured
- [ ] Payment provider in production mode
- [ ] Error monitoring enabled
- [ ] Rate limiting configured
- [ ] CORS settings properly configured
- [ ] SSL/TLS enabled
- [ ] Webhook endpoints secured

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Ensure you're using the correct key type (Secret vs Public)
2. **CORS Errors**: Check CORS configuration in `next.config.ts`
3. **Network Timeouts**: Adjust timeout settings in HTTP client
4. **Rate Limiting**: Implement proper rate limiting for production

### Debug Mode

Enable debug logging:

```typescript
const { customerInfo } = useRevenueCat({
  userId: 'user123',
  enableLogging: true, // Enable debug logs
});
```

## Security Considerations

1. **API Keys**: Never expose secret keys to the client
2. **User Validation**: Always validate user IDs on the server
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Input Validation**: Validate all inputs using Zod schemas
5. **HTTPS**: Always use HTTPS in production

## Support

- **RevenueCat Documentation**: [docs.revenuecat.com](https://docs.revenuecat.com)
- **RevenueCat Community**: [community.revenuecat.com](https://community.revenuecat.com)
- **API Reference**: [revenuecat.com/docs/api-v1](https://www.revenuecat.com/docs/api-v1)

## Next Steps

After setting up RevenueCat, you can:

1. Implement payment flows with your chosen payment provider
2. Set up webhooks for real-time subscription updates
3. Add analytics and monitoring
4. Implement subscription management UI
5. Add promotional codes and discounts
