/**
 * Sentry API Route
 * Server-side API for Sentry operations and health checks
 */

import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { createSuccessResponse, createErrorResponse } from '@/lib/apiResponse';
import {
  captureError,
  captureMessage,
  testSentryConnection,
  isSentryEnabled,
  getSentryEnvironment,
  getSentryRelease,
} from '@/lib/sentry';
import type { SentryHealthCheck, SentryAPIError } from '@/lib/types/sentry';

// =============================================================================
// GET - Health Check and Status
// =============================================================================

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';

    switch (action) {
      case 'health':
        return await handleHealthCheck();
      
      case 'test':
        return await handleConnectionTest();
      
      default:
        return createErrorResponse({
          code: 'INVALID_ACTION',
          message: `Unsupported action: ${action}. Supported actions: health, test`,
          timestamp: new Date().toISOString(),
        }, 400);
    }
  } catch (error) {
    console.error('Sentry API GET error:', error);
    
    const eventId = captureError(error as Error, {
      tags: {
        endpoint: '/api/sentry',
        method: 'GET',
        source: 'api-error',
      },
    });

    return createErrorResponse({
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      eventId,
    }, 500);
  }
}

// =============================================================================
// POST - Manual Error Reporting and Operations
// =============================================================================

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...data } = body;

    if (!action) {
      return createErrorResponse({
        code: 'MISSING_ACTION',
        message: 'Action parameter is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    switch (action) {
      case 'capture-error':
        return await handleCaptureError(data);
      
      case 'capture-message':
        return await handleCaptureMessage(data);
      
      case 'test-error':
        return await handleTestError();
      
      default:
        return createErrorResponse({
          code: 'INVALID_ACTION',
          message: `Unsupported action: ${action}. Supported actions: capture-error, capture-message, test-error`,
          timestamp: new Date().toISOString(),
        }, 400);
    }
  } catch (error) {
    console.error('Sentry API POST error:', error);
    
    const eventId = captureError(error as Error, {
      tags: {
        endpoint: '/api/sentry',
        method: 'POST',
        source: 'api-error',
      },
    });

    return createErrorResponse({
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      eventId,
    }, 500);
  }
}

// =============================================================================
// Handler Functions
// =============================================================================

/**
 * Handle health check request
 */
async function handleHealthCheck(): Promise<NextResponse> {
  const isEnabled = isSentryEnabled();
  const environment = getSentryEnvironment();
  const release = getSentryRelease();
  const lastEventId = Sentry.lastEventId();

  const healthCheck: SentryHealthCheck = {
    status: isEnabled ? 'healthy' : 'unhealthy',
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN ? '***configured***' : 'missing',
    environment,
    release,
    lastEventId,
    timestamp: new Date().toISOString(),
  };

  return createSuccessResponse(healthCheck);
}

/**
 * Handle connection test request
 */
async function handleConnectionTest(): Promise<NextResponse> {
  try {
    const isConnected = await testSentryConnection();
    
    return createSuccessResponse({
      connected: isConnected,
      message: isConnected ? 'Sentry connection successful' : 'Sentry connection failed',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return createErrorResponse({
      code: 'CONNECTION_TEST_FAILED',
      message: 'Failed to test Sentry connection',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle manual error capture
 */
async function handleCaptureError(data: any): Promise<NextResponse> {
  try {
    const { error, context } = data;

    if (!error) {
      return createErrorResponse({
        code: 'MISSING_ERROR',
        message: 'Error data is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // Create error object
    const errorObj = new Error(error.message || 'Manual error capture');
    if (error.stack) {
      errorObj.stack = error.stack;
    }

    // Capture error with context
    const eventId = captureError(errorObj, {
      ...context,
      tags: {
        ...context?.tags,
        source: 'manual-capture',
        endpoint: '/api/sentry',
      },
    });

    return createSuccessResponse({
      eventId,
      message: 'Error captured successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return createErrorResponse({
      code: 'CAPTURE_ERROR_FAILED',
      message: 'Failed to capture error',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle manual message capture
 */
async function handleCaptureMessage(data: any): Promise<NextResponse> {
  try {
    const { message, level = 'info', context } = data;

    if (!message) {
      return createErrorResponse({
        code: 'MISSING_MESSAGE',
        message: 'Message is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // Capture message with context
    const eventId = captureMessage(message, level, {
      ...context,
      tags: {
        ...context?.tags,
        source: 'manual-capture',
        endpoint: '/api/sentry',
      },
    });

    return createSuccessResponse({
      eventId,
      message: 'Message captured successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return createErrorResponse({
      code: 'CAPTURE_MESSAGE_FAILED',
      message: 'Failed to capture message',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle test error generation
 */
async function handleTestError(): Promise<NextResponse> {
  try {
    // Generate a test error
    const testError = new Error('Sentry API test error');
    
    const eventId = captureError(testError, {
      tags: {
        source: 'test-error',
        endpoint: '/api/sentry',
        test: 'true',
      },
      extra: {
        testTimestamp: new Date().toISOString(),
        userAgent: 'API Test',
      },
    });

    return createSuccessResponse({
      eventId,
      message: 'Test error generated and captured successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return createErrorResponse({
      code: 'TEST_ERROR_FAILED',
      message: 'Failed to generate test error',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

// =============================================================================
// Error Handling
// =============================================================================

/**
 * Handle unsupported HTTP methods
 */
export async function PUT() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'PUT method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'DELETE method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}

export async function PATCH() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'PATCH method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}
