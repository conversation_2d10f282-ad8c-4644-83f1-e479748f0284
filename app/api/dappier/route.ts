/**
 * Dappier API Route
 * Server-side API for Dappier AI search and custom copilots
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSuccessResponse, createErrorResponse } from '@/lib/apiResponse';
import {
  queryRealTimeData,
  performSearch,
  getRecommendations,
  sendCopilotMessage,
  testDappierConnection,
  isDappierEnabled,
  getDappierConfig,
} from '@/lib/dappier';
import { captureError } from '@/lib/sentry';
import type {
  RealTimeDataRequest,
  SearchRequest,
  RecommendationsRequest,
  CopilotRequest,
  DappierAPIError,
} from '@/lib/types/dappier';

// =============================================================================
// GET - Health Check and Status
// =============================================================================

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';

    switch (action) {
      case 'health':
        return await handleHealthCheck();
      
      case 'test':
        return await handleConnectionTest();
      
      case 'config':
        return await handleGetConfig();
      
      default:
        return createErrorResponse({
          code: 'INVALID_ACTION',
          message: `Unsupported action: ${action}. Supported actions: health, test, config`,
          timestamp: new Date().toISOString(),
        }, 400);
    }
  } catch (error) {
    console.error('Dappier API GET error:', error);
    
    const eventId = captureError(error as Error, {
      tags: {
        endpoint: '/api/dappier',
        method: 'GET',
        source: 'api-error',
      },
    });

    return createErrorResponse({
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      eventId,
    }, 500);
  }
}

// =============================================================================
// POST - AI Operations
// =============================================================================

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...data } = body;

    if (!action) {
      return createErrorResponse({
        code: 'MISSING_ACTION',
        message: 'Action parameter is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    if (!isDappierEnabled()) {
      return createErrorResponse({
        code: 'DAPPIER_DISABLED',
        message: 'Dappier integration is not enabled or configured',
        timestamp: new Date().toISOString(),
      }, 503);
    }

    switch (action) {
      case 'query':
        return await handleRealTimeQuery(data);
      
      case 'search':
        return await handleSearch(data);
      
      case 'recommendations':
        return await handleRecommendations(data);
      
      case 'copilot':
        return await handleCopilot(data);
      
      default:
        return createErrorResponse({
          code: 'INVALID_ACTION',
          message: `Unsupported action: ${action}. Supported actions: query, search, recommendations, copilot`,
          timestamp: new Date().toISOString(),
        }, 400);
    }
  } catch (error) {
    console.error('Dappier API POST error:', error);
    
    const eventId = captureError(error as Error, {
      tags: {
        endpoint: '/api/dappier',
        method: 'POST',
        source: 'api-error',
      },
    });

    return createErrorResponse({
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      eventId,
    }, 500);
  }
}

// =============================================================================
// Handler Functions
// =============================================================================

/**
 * Handle health check request
 */
async function handleHealthCheck(): Promise<NextResponse> {
  const isEnabled = isDappierEnabled();
  const config = getDappierConfig();

  const healthCheck = {
    status: isEnabled ? 'healthy' : 'unhealthy',
    enabled: isEnabled,
    apiKey: config.apiKey ? '***configured***' : 'missing',
    baseUrl: config.baseUrl,
    defaultModel: config.defaultModel,
    timestamp: new Date().toISOString(),
  };

  return createSuccessResponse(healthCheck);
}

/**
 * Handle connection test request
 */
async function handleConnectionTest(): Promise<NextResponse> {
  try {
    const response = await testDappierConnection();
    
    if (response.success) {
      return createSuccessResponse({
        connected: true,
        message: 'Dappier connection successful',
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } else {
      return createErrorResponse({
        code: 'CONNECTION_FAILED',
        message: 'Dappier connection failed',
        details: response.error,
        timestamp: new Date().toISOString(),
      }, 500);
    }
  } catch (error) {
    return createErrorResponse({
      code: 'CONNECTION_TEST_FAILED',
      message: 'Failed to test Dappier connection',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle get configuration request
 */
async function handleGetConfig(): Promise<NextResponse> {
  const config = getDappierConfig();
  
  // Return safe configuration (without sensitive data)
  const safeConfig = {
    baseUrl: config.baseUrl,
    defaultModel: config.defaultModel,
    timeout: config.timeout,
    maxRetries: config.maxRetries,
    enableCaching: config.enableCaching,
    cacheTtl: config.cacheTtl,
    apiKeyConfigured: !!config.apiKey,
  };

  return createSuccessResponse(safeConfig);
}

/**
 * Handle real-time data query
 */
async function handleRealTimeQuery(data: any): Promise<NextResponse> {
  try {
    const request: RealTimeDataRequest = {
      query: data.query,
      model: data.model,
      context: data.context,
      filters: data.filters,
      maxResults: data.maxResults,
      includeMetadata: data.includeMetadata,
    };

    if (!request.query) {
      return createErrorResponse({
        code: 'MISSING_QUERY',
        message: 'Query parameter is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const response = await queryRealTimeData(request);
    
    if (response.success) {
      return createSuccessResponse({
        data: response.data,
        metadata: response.metadata,
        timestamp: new Date().toISOString(),
      });
    } else {
      return createErrorResponse({
        code: 'QUERY_FAILED',
        message: 'Failed to execute real-time query',
        details: response.error,
        timestamp: new Date().toISOString(),
      }, 500);
    }
  } catch (error) {
    return createErrorResponse({
      code: 'QUERY_ERROR',
      message: 'Error processing real-time query',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle search request
 */
async function handleSearch(data: any): Promise<NextResponse> {
  try {
    const request: SearchRequest = {
      query: data.query,
      type: data.type,
      filters: data.filters,
      sort: data.sort,
      pagination: data.pagination,
      includeSnippets: data.includeSnippets,
      includeMetadata: data.includeMetadata,
    };

    if (!request.query) {
      return createErrorResponse({
        code: 'MISSING_QUERY',
        message: 'Query parameter is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const response = await performSearch(request);
    
    if (response.success) {
      return createSuccessResponse({
        data: response.data,
        metadata: response.metadata,
        timestamp: new Date().toISOString(),
      });
    } else {
      return createErrorResponse({
        code: 'SEARCH_FAILED',
        message: 'Failed to execute search',
        details: response.error,
        timestamp: new Date().toISOString(),
      }, 500);
    }
  } catch (error) {
    return createErrorResponse({
      code: 'SEARCH_ERROR',
      message: 'Error processing search request',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle recommendations request
 */
async function handleRecommendations(data: any): Promise<NextResponse> {
  try {
    const request: RecommendationsRequest = {
      userId: data.userId,
      context: data.context,
      type: data.type,
      maxResults: data.maxResults,
      includeReasons: data.includeReasons,
      excludeIds: data.excludeIds,
    };

    const response = await getRecommendations(request);
    
    if (response.success) {
      return createSuccessResponse({
        data: response.data,
        metadata: response.metadata,
        timestamp: new Date().toISOString(),
      });
    } else {
      return createErrorResponse({
        code: 'RECOMMENDATIONS_FAILED',
        message: 'Failed to get recommendations',
        details: response.error,
        timestamp: new Date().toISOString(),
      }, 500);
    }
  } catch (error) {
    return createErrorResponse({
      code: 'RECOMMENDATIONS_ERROR',
      message: 'Error processing recommendations request',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Handle copilot message
 */
async function handleCopilot(data: any): Promise<NextResponse> {
  try {
    const request: CopilotRequest = {
      message: data.message,
      conversationId: data.conversationId,
      userId: data.userId,
      context: data.context,
      personality: data.personality,
      capabilities: data.capabilities,
    };

    if (!request.message) {
      return createErrorResponse({
        code: 'MISSING_MESSAGE',
        message: 'Message parameter is required',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const response = await sendCopilotMessage(request);
    
    if (response.success) {
      return createSuccessResponse({
        data: response.data,
        metadata: response.metadata,
        timestamp: new Date().toISOString(),
      });
    } else {
      return createErrorResponse({
        code: 'COPILOT_FAILED',
        message: 'Failed to process copilot message',
        details: response.error,
        timestamp: new Date().toISOString(),
      }, 500);
    }
  } catch (error) {
    return createErrorResponse({
      code: 'COPILOT_ERROR',
      message: 'Error processing copilot request',
      details: { error: (error as Error).message },
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

// =============================================================================
// Error Handling
// =============================================================================

/**
 * Handle unsupported HTTP methods
 */
export async function PUT() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'PUT method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'DELETE method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}

export async function PATCH() {
  return createErrorResponse({
    code: 'METHOD_NOT_ALLOWED',
    message: 'PATCH method is not supported',
    timestamp: new Date().toISOString(),
  }, 405);
}
