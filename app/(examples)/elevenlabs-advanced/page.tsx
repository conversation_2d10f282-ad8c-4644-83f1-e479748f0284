'use client';

import { useState } from 'react';
import { toast, Toaster } from 'sonner';

import { TextToSpeechPromptBar } from '@/components/prompt-bar/text-to-speech';
import { AudioPlayer } from '@/components/audio/audio-player';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Trash2, 
  Download, 
  Play, 
  Pause,
  Volume2,
  Clock,
  FileAudio
} from 'lucide-react';

interface GeneratedSpeech {
  id: string;
  text: string;
  audioUrl: string;
  createdAt: Date;
  status: 'loading' | 'complete' | 'error';
  generationTime?: number;
}

export default function ElevenLabsAdvancedPage() {
  const [generatedSpeeches, setGeneratedSpeeches] = useState<GeneratedSpeech[]>([]);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);

  const handleGenerateStart = (text: string): string => {
    const id = `speech-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const newSpeech: GeneratedSpeech = {
      id,
      text,
      audioUrl: '',
      createdAt: new Date(),
      status: 'loading',
    };

    setGeneratedSpeeches(prev => [newSpeech, ...prev]);
    return id;
  };

  const handleGenerateComplete = (id: string, text: string, audioUrl: string) => {
    setGeneratedSpeeches(prev => 
      prev.map(speech => 
        speech.id === id 
          ? { ...speech, audioUrl, status: 'complete' as const }
          : speech
      )
    );
  };

  const handleDelete = (id: string) => {
    setGeneratedSpeeches(prev => prev.filter(speech => speech.id !== id));
    
    // Clean up blob URL
    const speech = generatedSpeeches.find(s => s.id === id);
    if (speech?.audioUrl && speech.audioUrl.startsWith('blob:')) {
      URL.revokeObjectURL(speech.audioUrl);
    }
    
    toast.success('Speech deleted');
  };

  const handleDownload = (speech: GeneratedSpeech) => {
    if (!speech.audioUrl) return;

    const link = document.createElement('a');
    link.href = speech.audioUrl;
    link.download = `elevenlabs-speech-${speech.id}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('Download started');
  };

  const handlePlayPause = (id: string) => {
    if (currentlyPlaying === id) {
      setCurrentlyPlaying(null);
    } else {
      setCurrentlyPlaying(id);
      // The AudioPlayer component will handle the actual playback
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <Toaster position="top-right" />
      
      {/* Header */}
      <div className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Advanced ElevenLabs Integration</h1>
        <p className="text-muted-foreground text-lg">
          Experience the full power of ElevenLabs with advanced voice controls, generation history, and professional audio management
        </p>
      </div>

      {/* Features Overview */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Enhanced Features
          </CardTitle>
          <CardDescription>
            This advanced integration includes all features from the official ElevenLabs template
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <FileAudio className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-semibold">Advanced Voice Settings</h3>
              <p className="text-sm text-muted-foreground">Stability, similarity, style, speed, and speaker boost controls</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Clock className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-semibold">Generation History</h3>
              <p className="text-sm text-muted-foreground">Track all your generated speeches with timestamps</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Play className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <h3 className="font-semibold">Audio Management</h3>
              <p className="text-sm text-muted-foreground">Play, pause, download, and manage your audio files</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Volume2 className="h-8 w-8 mx-auto mb-2 text-orange-500" />
              <h3 className="font-semibold">Featured Voices</h3>
              <p className="text-sm text-muted-foreground">Access to premium voices with accent information</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Text-to-Speech Interface */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Advanced Text-to-Speech Generator</CardTitle>
          <CardDescription>
            Generate high-quality speech with professional voice controls and settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TextToSpeechPromptBar
            onGenerateStart={handleGenerateStart}
            onGenerateComplete={handleGenerateComplete}
          />
        </CardContent>
      </Card>

      {/* Generation History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Generation History</span>
            <Badge variant="secondary">
              {generatedSpeeches.length} {generatedSpeeches.length === 1 ? 'item' : 'items'}
            </Badge>
          </CardTitle>
          <CardDescription>
            All your generated speeches with playback and download options
          </CardDescription>
        </CardHeader>
        <CardContent>
          {generatedSpeeches.length === 0 ? (
            <div className="text-center py-12">
              <FileAudio className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No speeches generated yet</h3>
              <p className="text-muted-foreground">
                Use the text-to-speech generator above to create your first speech
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {generatedSpeeches.map((speech, index) => (
                <div key={speech.id}>
                  <div className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant={speech.status === 'complete' ? 'default' : speech.status === 'loading' ? 'secondary' : 'destructive'}
                        >
                          {speech.status}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {formatTime(speech.createdAt)}
                        </span>
                        {speech.generationTime && (
                          <span className="text-sm text-muted-foreground">
                            • {Math.round(speech.generationTime)}ms
                          </span>
                        )}
                      </div>
                      
                      <p className="text-sm mb-3 break-words">
                        {truncateText(speech.text)}
                      </p>
                      
                      {speech.status === 'complete' && speech.audioUrl && (
                        <div className="mb-3">
                          <AudioPlayer 
                            audioBase64={speech.audioUrl}
                            autoplay={false}
                            className="max-w-md"
                          />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {speech.status === 'complete' && speech.audioUrl && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePlayPause(speech.id)}
                          >
                            {currentlyPlaying === speech.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(speech)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(speech.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {index < generatedSpeeches.length - 1 && <Separator />}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
