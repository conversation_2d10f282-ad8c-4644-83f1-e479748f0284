/**
 * Sentry Server Configuration
 * Server-side Sentry initialization for Next.js App Router
 */

import * as Sentry from '@sentry/nextjs';
import { getSentryEnvironment, getSentryRelease } from '@/lib/sentry';

// Initialize Sentry for server-side operations
Sentry.init({
  // Data Source Name (DSN) - tells <PERSON>try where to send events
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Environment (development, staging, production)
  environment: getSentryEnvironment(),

  // Release version for tracking deployments
  release: getSentryRelease(),

  // Performance Monitoring
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
  // We recommend adjusting this value in production
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '1.0'),

  // Capture Console
  // Automatically capture console.log, console.error, etc.
  integrations: [
    // Console logging integration
    Sentry.consoleLoggingIntegration({
      levels: ['error', 'warn'],
    }),

    // HTTP integration for tracking outgoing requests
    Sentry.httpIntegration({
      tracing: {
        // Capture outgoing HTTP requests
        shouldCreateSpanForRequest: (url) => {
          // Don't trace Sentry requests to avoid infinite loops
          return !url.includes('sentry.io');
        },
      },
    }),

    // Node.js specific integrations
    Sentry.nodeContextIntegration(),
    Sentry.localVariablesIntegration(),
    Sentry.requestDataIntegration(),
  ],

  // Enable experimental features
  _experiments: {
    // Enable logs
    enableLogs: true,
  },

  // Debug mode (only in development)
  debug: process.env.SENTRY_DEBUG === 'true' || process.env.NODE_ENV === 'development',

  // Send default PII (Personally Identifiable Information)
  // Set to true to include user IP, cookies, etc.
  sendDefaultPii: process.env.SENTRY_SEND_DEFAULT_PII === 'true',

  // Maximum breadcrumbs to keep
  maxBreadcrumbs: 100,

  // Attach stack traces to pure capture message calls
  attachStacktrace: true,

  // Before send hook - filter or modify events before sending
  beforeSend(event, hint) {
    // Don't send events in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out specific errors
    if (event.exception) {
      const error = hint.originalException;
      
      // Filter out network errors that are not actionable
      if (error instanceof Error) {
        if (
          error.message.includes('Network request failed') ||
          error.message.includes('fetch is not defined') ||
          error.message.includes('AbortError')
        ) {
          return null;
        }
      }
    }

    // Add server-specific context
    event.contexts = {
      ...event.contexts,
      server: {
        runtime: 'nodejs',
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };

    return event;
  },

  // Before send transaction hook
  beforeSendTransaction(event) {
    // Don't send transactions in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out health check transactions
    if (event.transaction?.includes('/api/health')) {
      return null;
    }

    return event;
  },

  // Error filtering
  ignoreErrors: [
    // Browser extensions
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
    'Script error.',
    
    // Network errors
    'NetworkError',
    'Failed to fetch',
    'Load failed',
    
    // Cancelled requests
    'AbortError',
    'The operation was aborted',
    
    // Common browser errors that aren't actionable
    'ChunkLoadError',
    'Loading chunk',
    'Loading CSS chunk',
  ],

  // Ignore specific URLs
  denyUrls: [
    // Browser extensions
    /extensions\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
    
    // Ad blockers
    /adnxs\.com/i,
    /adsystem\.google\.com/i,
    
    // Social media widgets
    /connect\.facebook\.net/i,
    /platform\.twitter\.com/i,
  ],

  // Transport options
  transport: Sentry.makeNodeTransport,

  // Stack trace options
  stackParser: Sentry.defaultStackParser,

  // Initial scope configuration
  initialScope: {
    tags: {
      component: 'server',
      runtime: 'nodejs',
    },
  },
});

// Export Sentry for server-side usage
export { Sentry };

// Log initialization in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Sentry server-side initialized:', {
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN ? '***configured***' : 'missing',
    environment: getSentryEnvironment(),
    release: getSentryRelease(),
    debug: process.env.SENTRY_DEBUG === 'true',
  });
}
