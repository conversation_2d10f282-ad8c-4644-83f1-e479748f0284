/**
 * Dappier AI Recommendations Component
 * Intelligent content recommendations with personalization
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useDappierRecommendations } from '@/hooks/useDappier';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Sparkles, 
  TrendingUp, 
  Star, 
  ExternalLink,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Clock,
  Target
} from 'lucide-react';
import type { Recommendation, RecommendationContext } from '@/lib/types/dappier';

interface RecommendationsProps {
  userId?: string;
  context?: RecommendationContext;
  type?: 'content' | 'product' | 'action' | 'mixed';
  maxResults?: number;
  title?: string;
  showReasons?: boolean;
  showActions?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onRecommendationClick?: (recommendation: Recommendation) => void;
  onRecommendationFeedback?: (recommendationId: string, feedback: 'positive' | 'negative') => void;
  className?: string;
}

export function Recommendations({
  userId,
  context,
  type = 'content',
  maxResults = 5,
  title = "Recommended for You",
  showReasons = true,
  showActions = true,
  autoRefresh = false,
  refreshInterval = 300000, // 5 minutes
  onRecommendationClick,
  onRecommendationFeedback,
  className = "",
}: RecommendationsProps) {
  const { recommend, isLoading, error } = useDappierRecommendations();
  
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [feedbackGiven, setFeedbackGiven] = useState<Set<string>>(new Set());

  // Load recommendations
  const loadRecommendations = useCallback(async () => {
    try {
      const response = await recommend(userId, {
        context,
        type,
        maxResults,
        includeReasons: showReasons,
      });
      
      setRecommendations(response.recommendations);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to load recommendations:', err);
    }
  }, [recommend, userId, context, type, maxResults, showReasons]);

  // Initial load
  useEffect(() => {
    loadRecommendations();
  }, [loadRecommendations]);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(loadRecommendations, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loadRecommendations]);

  // Handle recommendation click
  const handleRecommendationClick = useCallback((recommendation: Recommendation) => {
    if (onRecommendationClick) {
      onRecommendationClick(recommendation);
    }
    
    // Track click if URL is provided
    if (recommendation.url) {
      window.open(recommendation.url, '_blank', 'noopener,noreferrer');
    }
  }, [onRecommendationClick]);

  // Handle feedback
  const handleFeedback = useCallback((recommendationId: string, feedback: 'positive' | 'negative') => {
    setFeedbackGiven(prev => new Set(prev).add(recommendationId));
    
    if (onRecommendationFeedback) {
      onRecommendationFeedback(recommendationId, feedback);
    }
    
    // Here you could also send feedback to the API
    // await sendFeedback(recommendationId, feedback);
  }, [onRecommendationFeedback]);

  // Get recommendation type icon
  const getTypeIcon = useCallback((recType: string) => {
    switch (recType) {
      case 'content':
        return <Eye className="h-4 w-4" />;
      case 'product':
        return <Star className="h-4 w-4" />;
      case 'action':
        return <Target className="h-4 w-4" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  }, []);

  // Format confidence score
  const formatConfidence = useCallback((confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  }, []);

  // Format last updated time
  const formatLastUpdated = useCallback(() => {
    if (!lastUpdated) return '';
    
    const now = new Date();
    const diff = now.getTime() - lastUpdated.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }, [lastUpdated]);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              {title}
            </CardTitle>
            <CardDescription>
              AI-powered recommendations based on your preferences
              {lastUpdated && (
                <span className="ml-2 text-xs">
                  • Updated {formatLastUpdated()}
                </span>
              )}
            </CardDescription>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadRecommendations}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Loading State */}
        {isLoading && recommendations.length === 0 && (
          <div className="space-y-3">
            {Array.from({ length: maxResults }).map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-full" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <div className="text-destructive mb-2">
              <Sparkles className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Failed to load recommendations</p>
              <p className="text-xs text-muted-foreground mt-1">{error.message}</p>
            </div>
            <Button variant="outline" size="sm" onClick={loadRecommendations}>
              Try Again
            </Button>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && recommendations.length === 0 && (
          <div className="text-center py-8">
            <Sparkles className="h-8 w-8 mx-auto mb-2 text-muted-foreground opacity-50" />
            <p className="text-sm text-muted-foreground">
              No recommendations available at the moment
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Try using the app more to get personalized suggestions
            </p>
          </div>
        )}

        {/* Recommendations List */}
        {recommendations.length > 0 && (
          <div className="space-y-4">
            {recommendations.map((recommendation, index) => (
              <div
                key={recommendation.id}
                className="group border rounded-lg p-4 hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => handleRecommendationClick(recommendation)}
              >
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="p-2 rounded-md bg-primary/10 flex-shrink-0">
                        {getTypeIcon(recommendation.type)}
                      </div>
                      
                      <div className="space-y-1 flex-1 min-w-0">
                        <h3 className="font-medium text-sm group-hover:text-primary transition-colors line-clamp-2">
                          {recommendation.title}
                        </h3>
                        
                        {recommendation.description && (
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {recommendation.description}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {recommendation.url && (
                      <ExternalLink className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0" />
                    )}
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge variant="secondary" className="text-xs">
                      {recommendation.type}
                    </Badge>
                    
                    <Badge variant="outline" className="text-xs">
                      {formatConfidence(recommendation.confidence)} match
                    </Badge>
                    
                    {recommendation.score && (
                      <Badge variant="outline" className="text-xs">
                        Score: {Math.round(recommendation.score * 100)}
                      </Badge>
                    )}
                  </div>

                  {/* Reasons */}
                  {showReasons && recommendation.reasons && recommendation.reasons.length > 0 && (
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-muted-foreground">Why this was recommended:</p>
                      <div className="flex flex-wrap gap-1">
                        {recommendation.reasons.slice(0, 3).map((reason, reasonIndex) => (
                          <Badge key={reasonIndex} variant="outline" className="text-xs">
                            {reason}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  {showActions && (
                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">Was this helpful?</span>
                        
                        {!feedbackGiven.has(recommendation.id) ? (
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFeedback(recommendation.id, 'positive');
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFeedback(recommendation.id, 'negative');
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <Badge variant="outline" className="text-xs">
                            Thanks for feedback!
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>#{index + 1}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Load More */}
        {recommendations.length > 0 && recommendations.length >= maxResults && (
          <div className="text-center pt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Could implement pagination here
                console.log('Load more recommendations');
              }}
              disabled={isLoading}
            >
              Load More Recommendations
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
