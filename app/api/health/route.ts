/**
 * Health Check API Route
 * Provides system health status and service connectivity checks
 */

import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  validateMethod,
  validateEnvVars,
  withErrorHandling,
} from '@/lib/apiResponse';
import { createDefaultHttpClient } from '@/lib/httpClient';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  error?: string;
  version?: string;
}

interface SystemHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  services: HealthCheckResult[];
  environment: string;
  version: string;
}

/**
 * GET /api/health
 * Returns system health status and service connectivity
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  const methodError = validateMethod(request, ['GET']);
  if (methodError) return methodError;

  const services: HealthCheckResult[] = [];

  // Check RevenueCat connectivity
  const revenueCatHealth = await checkRevenueCatHealth();
  services.push(revenueCatHealth);

  // Check ElevenLabs connectivity
  const elevenLabsHealth = await checkElevenLabsHealth();
  services.push(elevenLabsHealth);

  // Add more service checks as we implement them
  // const nodelyHealth = await checkNodelyHealth();
  // services.push(nodelyHealth);

  // Determine overall system health
  const hasUnhealthy = services.some(service => service.status === 'unhealthy');
  const hasDegraded = services.some(service => service.status === 'degraded');
  
  let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
  if (hasUnhealthy) {
    overallStatus = 'unhealthy';
  } else if (hasDegraded) {
    overallStatus = 'degraded';
  }

  const health: SystemHealth = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services,
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
  };

  const statusCode = overallStatus === 'healthy' ? 200 : 503;
  
  if (overallStatus === 'healthy') {
    return createSuccessResponse(health);
  } else {
    return createErrorResponse({
      code: 'HEALTH_CHECK_FAILED',
      message: `System health check failed: ${overallStatus}`,
      details: health,
      timestamp: new Date().toISOString(),
    }, statusCode);
  }
});

/**
 * Check RevenueCat service health
 */
async function checkRevenueCatHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Check if required environment variables are present
    const requiredVars = {
      REVENUECAT_SECRET_KEY: process.env.REVENUECAT_SECRET_KEY,
      REVENUECAT_PUBLIC_KEY: process.env.REVENUECAT_PUBLIC_KEY,
    };

    const missing = validateEnvVars(requiredVars);
    if (missing.length > 0) {
      return {
        service: 'RevenueCat',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: `Missing environment variables: ${missing.join(', ')}`,
      };
    }

    // Test API connectivity with a simple request
    const client = createDefaultHttpClient('https://api.revenuecat.com/v1');
    client.setAuthHeader(requiredVars.REVENUECAT_SECRET_KEY!);

    // Make a test request to check connectivity
    // Note: This will fail if the API key is invalid, but that's expected for health checks
    const response = await client.get('/subscribers/health_check_user', {}, {
      'X-Is-Sandbox': process.env.REVENUECAT_ENVIRONMENT === 'sandbox' ? 'true' : 'false',
    });

    const responseTime = Date.now() - startTime;

    // RevenueCat API will return 404 for non-existent users, which is expected
    // We consider 404 as healthy since it means the API is responding
    if (response.success || (response.error?.code === 404)) {
      return {
        service: 'RevenueCat',
        status: 'healthy',
        responseTime,
        version: 'v1',
      };
    }

    // Check for authentication errors
    if (response.error?.code === 401 || response.error?.code === 403) {
      return {
        service: 'RevenueCat',
        status: 'unhealthy',
        responseTime,
        error: 'Authentication failed - check API keys',
      };
    }

    // Other errors might indicate degraded service
    return {
      service: 'RevenueCat',
      status: 'degraded',
      responseTime,
      error: response.error?.message || 'Unknown error',
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      service: 'RevenueCat',
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Connection failed',
    };
  }
}

/**
 * Check ElevenLabs service health
 */
async function checkElevenLabsHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();

  try {
    // Check if required environment variables are present
    const requiredVars = {
      ELEVENLABS_API_KEY: process.env.ELEVENLABS_API_KEY,
      NEXT_PUBLIC_ELEVENLABS_API_KEY: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
    };

    const missing = validateEnvVars(requiredVars);
    if (missing.length > 0) {
      return {
        service: 'ElevenLabs',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: `Missing environment variables: ${missing.join(', ')}`,
      };
    }

    // Test API connectivity with REST API
    const response = await fetch('https://api.elevenlabs.io/v1/user', {
      headers: {
        'xi-api-key': requiredVars.ELEVENLABS_API_KEY!,
      },
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      return {
        service: 'ElevenLabs',
        status: 'healthy',
        responseTime,
        version: 'v1',
      };
    }

    return {
      service: 'ElevenLabs',
      status: 'degraded',
      responseTime,
      error: `HTTP ${response.status}: ${response.statusText}`,
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;

    // Check for authentication errors
    if (error && typeof error === 'object' && 'status' in error) {
      const httpError = error as { status: number; message?: string };
      if (httpError.status === 401 || httpError.status === 403) {
        return {
          service: 'ElevenLabs',
          status: 'unhealthy',
          responseTime,
          error: 'Authentication failed - check API keys',
        };
      }
    }

    return {
      service: 'ElevenLabs',
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Connection failed',
    };
  }
}

/**
 * Check database health (placeholder for future implementation)
 */
// async function checkDatabaseHealth(): Promise<HealthCheckResult> {
//   const startTime = Date.now();

//   try {
//     // Placeholder for database health check
//     // const db = await connectToDatabase();
//     // await db.query('SELECT 1');

//     return {
//       service: 'Database',
//       status: 'healthy',
//       responseTime: Date.now() - startTime,
//     };
//   } catch (error) {
//     return {
//       service: 'Database',
//       status: 'unhealthy',
//       responseTime: Date.now() - startTime,
//       error: error instanceof Error ? error.message : 'Connection failed',
//     };
//   }
// }

/**
 * Check Redis health (placeholder for future implementation)
 */
// async function checkRedisHealth(): Promise<HealthCheckResult> {
//   const startTime = Date.now();

//   try {
//     // Placeholder for Redis health check
//     // const redis = await connectToRedis();
//     // await redis.ping();

//     return {
//       service: 'Redis',
//       status: 'healthy',
//       responseTime: Date.now() - startTime,
//     };
//   } catch (error) {
//     return {
//       service: 'Redis',
//       status: 'unhealthy',
//       responseTime: Date.now() - startTime,
//       error: error instanceof Error ? error.message : 'Connection failed',
//     };
//   }
// }

/**
 * OPTIONS /api/health
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
