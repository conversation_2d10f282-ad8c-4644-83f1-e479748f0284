/**
 * RevenueCat React Hook
 * Provides client-side interface for RevenueCat operations
 * Prioritizes RevenueCat JavaScript SDK with fallback to REST API
 */

'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import useSWR from 'swr';
import { Purchases } from '@revenuecat/purchases-js';
import {
  Subscriber,
  Offering,
  AttributionData,
  UseRevenueCatOptions,
  UseRevenueCatReturn,
  RevenueCatError,
  OfferingsResponse,
} from '@/lib/types/revenuecat';

// RevenueCat SDK Configuration
interface RevenueCatSDKConfig {
  apiKey: string;
  userId: string;
  enableLogging?: boolean;
}

// SDK Error Handler
const handleSDKError = (error: unknown): RevenueCatError => {
  if (error && typeof error === 'object' && 'message' in error) {
    return {
      code: (error as any).errorCode || 0,
      message: (error as Error).message,
    };
  }
  return {
    code: 0,
    message: error instanceof Error ? error.message : 'Unknown error occurred',
  };
};

/**
 * Main RevenueCat hook using the JavaScript SDK
 */
export function useRevenueCat(options: UseRevenueCatOptions = {}): UseRevenueCatReturn {
  const { userId, apiKey, enableLogging = false } = options;

  const [customerInfo, setCustomerInfo] = useState<Subscriber | null>(null);
  const [offerings, setOfferings] = useState<Offering[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RevenueCatError | null>(null);
  const purchasesRef = useRef<InstanceType<typeof Purchases> | null>(null);
  const isConfiguredRef = useRef(false);

  // Get API key from environment if not provided
  const effectiveApiKey = apiKey || process.env.NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY;

  // Configure RevenueCat SDK
  useEffect(() => {
    if (!effectiveApiKey || !userId || isConfiguredRef.current) {
      return;
    }

    try {
      if (enableLogging) {
        Purchases.setLogLevel('DEBUG' as any);
      }

      const purchases = Purchases.configure(effectiveApiKey, userId);
      purchasesRef.current = purchases;
      isConfiguredRef.current = true;

      if (enableLogging) {
        console.log('RevenueCat SDK configured successfully for user:', userId);
      }
    } catch (err) {
      const error = handleSDKError(err);
      setError(error);
      console.error('Failed to configure RevenueCat SDK:', error);
    }
  }, [effectiveApiKey, userId, enableLogging]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (purchasesRef.current && isConfiguredRef.current) {
        try {
          purchasesRef.current.close();
          isConfiguredRef.current = false;
        } catch (err) {
          console.warn('Error closing RevenueCat SDK:', err);
        }
      }
    };
  }, []);

  // Refresh customer info using SDK
  const refreshCustomerInfo = useCallback(async (): Promise<void> => {
    if (!purchasesRef.current || !isConfiguredRef.current) {
      throw new Error('RevenueCat SDK not configured');
    }

    try {
      setIsLoading(true);
      setError(null);

      const customerInfoResult = await purchasesRef.current.getCustomerInfo();

      // Convert SDK response to our type format
      const convertedCustomerInfo: Subscriber = {
        app_user_id: customerInfoResult.originalAppUserId,
        original_app_user_id: customerInfoResult.originalAppUserId,
        request_date: new Date().toISOString(),
        request_date_ms: Date.now(),
        first_seen: (customerInfoResult as any).firstSeen || new Date().toISOString(),
        last_seen: (customerInfoResult as any).latestExpirationDate || new Date().toISOString(),
        management_url: customerInfoResult.managementURL || undefined,
        non_subscriptions: {},
        subscriptions: {},
        entitlements: (customerInfoResult.entitlements as any) || {},
      };

      setCustomerInfo(convertedCustomerInfo);

      if (enableLogging) {
        console.log('Customer info refreshed:', convertedCustomerInfo);
      }
    } catch (err) {
      const error = handleSDKError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [enableLogging]);

  // Refresh offerings using SDK
  const refreshOfferings = useCallback(async (): Promise<void> => {
    if (!purchasesRef.current || !isConfiguredRef.current) {
      throw new Error('RevenueCat SDK not configured');
    }

    try {
      setIsLoading(true);
      setError(null);

      const offeringsResult = await purchasesRef.current.getOfferings();

      // Convert SDK response to our type format
      const convertedOfferings: Offering[] = Object.values(offeringsResult.all || {}).map(offering => ({
        description: offering.serverDescription,
        identifier: offering.identifier,
        metadata: offering.metadata || undefined,
        packages: offering.availablePackages.map(pkg => ({
          identifier: pkg.identifier,
          platform_product_identifier: (pkg as any).product?.identifier || pkg.identifier,
        })),
      }));

      setOfferings(convertedOfferings);

      if (enableLogging) {
        console.log('Offerings refreshed:', convertedOfferings);
      }
    } catch (err) {
      const error = handleSDKError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [enableLogging]);

  // Purchase package using SDK
  const purchasePackage = useCallback(async (packageIdentifier: string): Promise<void> => {
    if (!purchasesRef.current || !isConfiguredRef.current) {
      throw new Error('RevenueCat SDK not configured');
    }

    try {
      setIsLoading(true);
      setError(null);

      if (enableLogging) {
        console.log('RevenueCat: Initiating purchase for package:', packageIdentifier);
      }

      // First get offerings to find the package
      const offeringsResult = await purchasesRef.current.getOfferings();
      let targetPackage = null;

      // Search for the package in all offerings
      for (const offering of Object.values(offeringsResult.all || {})) {
        targetPackage = offering.availablePackages.find(pkg => pkg.identifier === packageIdentifier);
        if (targetPackage) break;
      }

      if (!targetPackage) {
        throw new Error(`Package with identifier "${packageIdentifier}" not found`);
      }

      // Perform the purchase using the SDK
      const purchaseResult = await purchasesRef.current.purchase({
        rcPackage: targetPackage,
      });

      // Update customer info after successful purchase
      if (purchaseResult.customerInfo) {
        const convertedCustomerInfo: Subscriber = {
          app_user_id: purchaseResult.customerInfo.originalAppUserId,
          original_app_user_id: purchaseResult.customerInfo.originalAppUserId,
          request_date: new Date().toISOString(),
          request_date_ms: Date.now(),
          first_seen: (purchaseResult.customerInfo as any).firstSeen || new Date().toISOString(),
          last_seen: (purchaseResult.customerInfo as any).latestExpirationDate || new Date().toISOString(),
          management_url: purchaseResult.customerInfo.managementURL || undefined,
          non_subscriptions: {},
          subscriptions: {},
          entitlements: (purchaseResult.customerInfo.entitlements as any) || {},
        };
        setCustomerInfo(convertedCustomerInfo);
      }

      if (enableLogging) {
        console.log('Purchase completed successfully:', purchaseResult);
      }

    } catch (err) {
      const error = handleSDKError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [enableLogging]);

  // Auto-load data when SDK is configured
  useEffect(() => {
    if (isConfiguredRef.current && purchasesRef.current) {
      refreshCustomerInfo().catch(err => {
        if (enableLogging) {
          console.warn('Failed to auto-load customer info:', err);
        }
      });
      refreshOfferings().catch(err => {
        if (enableLogging) {
          console.warn('Failed to auto-load offerings:', err);
        }
      });
    }
  }, [refreshCustomerInfo, refreshOfferings, enableLogging]);

  return {
    customerInfo,
    offerings,
    isLoading,
    error,
    refreshCustomerInfo,
    refreshOfferings,
    purchasePackage,
  };
}

/**
 * Hook for adding attribution data (uses REST API as SDK doesn't support this)
 */
export function useRevenueCatAttribution() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RevenueCatError | null>(null);

  const addAttribution = useCallback(async (
    userId: string,
    attributionData: AttributionData
  ): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/revenuecat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'add-attribution',
          app_user_id: userId,
          attribution_data: attributionData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Failed to add attribution data');
      }

    } catch (err) {
      const error: RevenueCatError = {
        code: 0,
        message: err instanceof Error ? err.message : 'Failed to add attribution data',
      };
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    addAttribution,
    isLoading,
    error,
  };
}

/**
 * Hook for processing receipts (uses REST API as SDK doesn't support this)
 */
export function useRevenueCatReceipts() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RevenueCatError | null>(null);

  const processReceipt = useCallback(async (
    userId: string,
    fetchToken: string,
    attributes?: Record<string, string>
  ): Promise<unknown> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/revenuecat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'process-receipt',
          app_user_id: userId,
          fetch_token: fetchToken,
          attributes,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Failed to process receipt');
      }

      return data.data;

    } catch (err) {
      const error: RevenueCatError = {
        code: 0,
        message: err instanceof Error ? err.message : 'Failed to process receipt',
      };
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    processReceipt,
    isLoading,
    error,
  };
}

/**
 * Hook for checking subscription status using SDK
 */
export function useSubscriptionStatus(userId?: string) {
  const { customerInfo, isLoading, error } = useRevenueCat({ userId });

  const isSubscribed = customerInfo?.entitlements &&
    Object.keys(customerInfo.entitlements).length > 0;

  const activeSubscriptions = customerInfo?.subscriptions
    ? Object.entries(customerInfo.subscriptions).filter(([_, subscription]) => {
        const expiresDate = new Date(subscription.expires_date);
        return expiresDate > new Date();
      })
    : [];

  return {
    isSubscribed,
    activeSubscriptions,
    customerInfo,
    isLoading,
    error,
  };
}

/**
 * Hook for checking specific entitlements using SDK
 */
export function useEntitlementCheck(userId?: string, entitlementId?: string) {
  const [isEntitled, setIsEntitled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RevenueCatError | null>(null);

  useEffect(() => {
    if (!userId || !entitlementId) {
      setIsEntitled(false);
      return;
    }

    const checkEntitlement = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!Purchases.isConfigured()) {
          throw new Error('RevenueCat SDK not configured');
        }

        const purchases = Purchases.getSharedInstance();
        const entitled = await purchases.isEntitledTo(entitlementId);
        setIsEntitled(entitled);

      } catch (err) {
        const error = handleSDKError(err);
        setError(error);
        setIsEntitled(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkEntitlement();
  }, [userId, entitlementId]);

  return {
    isEntitled,
    isLoading,
    error,
  };
}

/**
 * Hook for managing RevenueCat configuration
 */
export function useRevenueCatConfig() {
  const [config, setConfig] = useState({
    enableLogging: false,
    userId: null as string | null,
    apiKey: null as string | null,
  });

  const updateConfig = useCallback((newConfig: Partial<typeof config>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  const isConfigured = Purchases.isConfigured();
  const isSandbox = isConfigured ? Purchases.getSharedInstance().isSandbox() : false;

  return {
    config,
    updateConfig,
    isConfigured,
    isSandbox,
  };
}

/**
 * Hook for user management using SDK
 */
export function useRevenueCatUser() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RevenueCatError | null>(null);

  const changeUser = useCallback(async (newUserId: string): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      if (!Purchases.isConfigured()) {
        throw new Error('RevenueCat SDK not configured');
      }

      const purchases = Purchases.getSharedInstance();
      await purchases.changeUser(newUserId);

    } catch (err) {
      const error = handleSDKError(err);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getCurrentUserId = useCallback((): string | null => {
    try {
      if (!Purchases.isConfigured()) {
        return null;
      }

      const purchases = Purchases.getSharedInstance();
      return purchases.getAppUserId();
    } catch {
      return null;
    }
  }, []);

  return {
    changeUser,
    getCurrentUserId,
    isLoading,
    error,
  };
}
