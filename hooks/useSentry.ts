/**
 * Sentry React Hook
 * Provides client-side interface for Sentry operations
 */

'use client';

import { useCallback, useEffect, useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import {
  captureError,
  captureMessage,
  setUser,
  clearUser,
  setTag,
  setTags,
  setContext,
  setExtra,
  addBreadcrumb,
  startTransaction,
  startSpan,
  captureReplay,
  isSentryEnabled,
  testSentryConnection,
} from '@/lib/sentry';
import type {
  UseSentryOptions,
  UseSentryReturn,
  SentryUser,
  ErrorCaptureOptions,
  SentryBreadcrumb,
  TransactionContext,
  SpanData,
  SentrySeverity,
} from '@/lib/types/sentry';

/**
 * Default options for the Sentry hook
 */
const defaultOptions: UseSentryOptions = {
  enablePerformanceMonitoring: true,
  enableSessionReplay: true,
  enableUserFeedback: false,
  autoCapture: true,
  defaultTags: {},
  defaultUser: undefined,
};

/**
 * Sentry React hook for client-side error monitoring and performance tracking
 */
export function useSentry(options: UseSentryOptions = {}): UseSentryReturn {
  const config = { ...defaultOptions, ...options };
  const [isEnabled, setIsEnabled] = useState(false);
  const [lastEventId, setLastEventId] = useState<string | null>(null);

  // Initialize Sentry configuration
  useEffect(() => {
    const enabled = isSentryEnabled();
    setIsEnabled(enabled);

    if (enabled) {
      // Set default tags
      if (config.defaultTags) {
        setTags(config.defaultTags);
      }

      // Set default user
      if (config.defaultUser) {
        setUser(config.defaultUser);
      }

      // Test connection in development
      if (process.env.NODE_ENV === 'development') {
        testSentryConnection().then((connected) => {
          console.log('🔍 Sentry connection test:', connected ? 'success' : 'failed');
        });
      }
    }
  }, [config.defaultTags, config.defaultUser]);

  // Error handling functions
  const handleCaptureError = useCallback((
    error: Error,
    context?: ErrorCaptureOptions
  ): string => {
    const eventId = captureError(error, context);
    setLastEventId(eventId);
    return eventId;
  }, []);

  const handleCaptureMessage = useCallback((
    message: string,
    level: SentrySeverity = 'info',
    context?: ErrorCaptureOptions
  ): string => {
    const eventId = captureMessage(message, level, context);
    setLastEventId(eventId);
    return eventId;
  }, []);

  const handleCaptureException = useCallback((
    exception: any,
    context?: ErrorCaptureOptions
  ): string => {
    const eventId = Sentry.captureException(exception, context);
    setLastEventId(eventId);
    return eventId;
  }, []);

  // User context functions
  const handleSetUser = useCallback((user: SentryUser): void => {
    setUser(user);
    
    // Add breadcrumb for user context change
    addBreadcrumb({
      category: 'auth',
      message: `User context updated: ${user.id || user.email || 'anonymous'}`,
      level: 'info',
      data: {
        userId: user.id,
        email: user.email,
        username: user.username,
      },
    });
  }, []);

  const handleClearUser = useCallback((): void => {
    clearUser();
    
    // Add breadcrumb for user context clear
    addBreadcrumb({
      category: 'auth',
      message: 'User context cleared',
      level: 'info',
    });
  }, []);

  // Tag and context functions
  const handleSetTag = useCallback((key: string, value: string): void => {
    setTag(key, value);
  }, []);

  const handleSetTags = useCallback((tags: Record<string, string>): void => {
    setTags(tags);
  }, []);

  const handleSetContext = useCallback((key: string, context: any): void => {
    setContext(key, context);
  }, []);

  const handleSetExtra = useCallback((key: string, extra: any): void => {
    setExtra(key, extra);
  }, []);

  // Breadcrumb function
  const handleAddBreadcrumb = useCallback((breadcrumb: SentryBreadcrumb): void => {
    addBreadcrumb(breadcrumb);
  }, []);

  // Performance monitoring functions
  const handleStartTransaction = useCallback((context: TransactionContext): any => {
    if (!config.enablePerformanceMonitoring) {
      return {
        setTag: () => {},
        setData: () => {},
        finish: () => {},
      };
    }

    return startTransaction(context);
  }, [config.enablePerformanceMonitoring]);

  const handleStartSpan = useCallback(async (spanData: SpanData): Promise<any> => {
    if (!config.enablePerformanceMonitoring) {
      return Promise.resolve();
    }

    return startSpan(spanData, async () => {
      // Span callback - this would be provided by the caller
      return Promise.resolve();
    });
  }, [config.enablePerformanceMonitoring]);

  // Session replay function
  const handleCaptureReplay = useCallback((): void => {
    if (!config.enableSessionReplay) {
      return;
    }

    captureReplay();
  }, [config.enableSessionReplay]);

  // User feedback function
  const handleShowUserFeedback = useCallback((): void => {
    if (!config.enableUserFeedback || !isEnabled) {
      return;
    }

    // Show user feedback dialog
    Sentry.showReportDialog({
      eventId: lastEventId || undefined,
      user: {
        email: '',
        name: '',
      },
    });
  }, [config.enableUserFeedback, isEnabled, lastEventId]);

  // Auto-capture unhandled errors
  useEffect(() => {
    if (!config.autoCapture || !isEnabled) {
      return;
    }

    const handleUnhandledError = (event: ErrorEvent) => {
      handleCaptureError(new Error(event.message), {
        extra: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
        tags: {
          source: 'unhandled-error',
        },
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      handleCaptureError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
        extra: {
          reason: event.reason,
        },
        tags: {
          source: 'unhandled-rejection',
        },
      });
    };

    window.addEventListener('error', handleUnhandledError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleUnhandledError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [config.autoCapture, isEnabled, handleCaptureError]);

  return {
    // Error handling
    captureError: handleCaptureError,
    captureMessage: handleCaptureMessage,
    captureException: handleCaptureException,
    
    // User context
    setUser: handleSetUser,
    clearUser: handleClearUser,
    
    // Tags and context
    setTag: handleSetTag,
    setTags: handleSetTags,
    setContext: handleSetContext,
    setExtra: handleSetExtra,
    
    // Breadcrumbs
    addBreadcrumb: handleAddBreadcrumb,
    
    // Performance monitoring
    startTransaction: handleStartTransaction,
    startSpan: handleStartSpan,
    
    // Session replay
    captureReplay: handleCaptureReplay,
    
    // User feedback
    showUserFeedback: handleShowUserFeedback,
    
    // Utilities
    isEnabled,
    lastEventId,
  };
}

/**
 * Hook for capturing API errors with automatic context
 */
export function useSentryAPIError() {
  const { captureError } = useSentry();

  return useCallback((
    error: Error,
    endpoint: string,
    method: string,
    statusCode: number,
    requestId?: string,
    duration?: number
  ) => {
    return captureError(error, {
      tags: {
        endpoint,
        method,
        statusCode: statusCode.toString(),
        source: 'api-error',
      },
      extra: {
        requestId,
        duration,
      },
      contexts: {
        api: {
          endpoint,
          method,
          statusCode,
          duration,
        },
      },
      fingerprint: [
        'api-error',
        endpoint,
        method,
        statusCode.toString(),
      ],
    });
  }, [captureError]);
}

/**
 * Hook for performance monitoring with automatic span creation
 */
export function useSentryPerformance() {
  const { startSpan, isEnabled } = useSentry();

  return useCallback(async <T>(
    operation: string,
    description: string,
    callback: () => Promise<T> | T,
    tags?: Record<string, string>
  ): Promise<T> => {
    if (!isEnabled) {
      return await callback();
    }

    return await startSpan({
      op: operation,
      description,
      tags,
    });
  }, [startSpan, isEnabled]);
}
