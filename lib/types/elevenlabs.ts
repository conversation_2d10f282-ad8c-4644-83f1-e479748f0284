/**
 * ElevenLabs TypeScript Type Definitions
 * Based on ElevenLabs API v1 and JavaScript SDK
 * Documentation: https://elevenlabs.io/docs/api-reference
 */

// Base types
export interface ElevenLabsError {
  code: number;
  message: string;
  detail?: string;
}

// Voice types
export interface Voice {
  voice_id: string;
  name: string;
  samples?: VoiceSample[];
  category: string;
  fine_tuning: FineTuning;
  labels: Record<string, string>;
  description?: string;
  preview_url?: string;
  available_for_tiers: string[];
  settings?: VoiceSettings;
  sharing?: VoiceSharing;
  high_quality_base_model_ids: string[];
  safety_control?: string;
  voice_verification?: VoiceVerification;
  permission_on_resource?: string;
  is_legacy?: boolean;
  is_mixed?: boolean;
}

export interface VoiceSample {
  sample_id: string;
  file_name: string;
  mime_type: string;
  size_bytes: number;
  hash: string;
}

export interface FineTuning {
  model_id?: string;
  is_allowed_to_fine_tune: boolean;
  finetuning_requested: boolean;
  finetuning_state: 'not_started' | 'is_fine_tuning' | 'fine_tuned';
  verification_attempts?: VerificationAttempt[];
  verification_failures: string[];
  verification_attempts_count: number;
  slice_ids?: string[];
  manual_verification?: ManualVerification;
  manual_verification_requested: boolean;
}

export interface VerificationAttempt {
  text: string;
  date_unix: number;
  accepted: boolean;
  similarity: number;
  levenshtein_distance: number;
  recording: Recording;
}

export interface Recording {
  recording_id: string;
  mime_type: string;
  size_bytes: number;
  upload_date_unix: number;
  transcription: string;
}

export interface ManualVerification {
  extra_text: string;
  request_time_unix: number;
  files: ManualVerificationFile[];
}

export interface ManualVerificationFile {
  file_id: string;
  file_name: string;
  mime_type: string;
  size_bytes: number;
  upload_date_unix: number;
}

export interface VoiceSettings {
  stability: number;
  similarity_boost: number;
  style?: number;
  use_speaker_boost?: boolean;
  speed?: number; // New: Speed control (0.7-1.2)
}

export interface VoiceSharing {
  status: 'enabled' | 'disabled' | 'copied' | 'mixed';
  history_item_sample_id?: string;
  original_voice_id?: string;
  public_owner_id?: string;
  liked_by_count: number;
  cloned_by_count: number;
  name?: string;
  description?: string;
  labels?: Record<string, string>;
  review_status?: string;
  review_message?: string;
  enabled_in_library: boolean;
  instagram_username?: string;
  twitter_username?: string;
  youtube_username?: string;
  tiktok_username?: string;
}

export interface VoiceVerification {
  requires_verification: boolean;
  is_verified: boolean;
  verification_failures: string[];
  verification_attempts_count: number;
  language?: string;
}

// Text-to-Speech types
export interface TextToSpeechRequest {
  text: string;
  model_id?: string;
  language_code?: string;
  voice_settings?: VoiceSettings;
  pronunciation_dictionary_locators?: PronunciationDictionaryLocator[];
  seed?: number;
  previous_text?: string;
  next_text?: string;
  previous_request_ids?: string[];
  next_request_ids?: string[];
  apply_text_normalization?: 'auto' | 'on' | 'off';
  apply_language_text_normalization?: boolean;
  use_pvc_as_ivc?: boolean;
}

export interface PronunciationDictionaryLocator {
  pronunciation_dictionary_id: string;
  version_id: string;
}

export interface TextToSpeechResponse {
  audio: ArrayBuffer;
  request_id?: string;
}

// Streaming types
export interface StreamingRequest extends TextToSpeechRequest {
  voice_id: string;
  output_format?: OutputFormat;
  enable_logging?: boolean;
  optimize_streaming_latency?: number;
}

// Model types
export interface Model {
  model_id: string;
  name: string;
  can_be_finetuned: boolean;
  can_do_text_to_speech: boolean;
  can_do_voice_conversion: boolean;
  can_use_style: boolean;
  can_use_speaker_boost: boolean;
  serves_pro_voices: boolean;
  serves_premium_voices: boolean;
  token_cost_factor: number;
  description: string;
  requires_alpha_access: boolean;
  max_characters_request_free_tier: number;
  max_characters_request_subscribed_tier: number;
  maximum_text_length_per_request: number;
  languages: ModelLanguage[];
}

export interface ModelLanguage {
  language_id: string;
  name: string;
}

// History types
export interface HistoryItem {
  history_item_id: string;
  request_id: string;
  voice_id: string;
  voice_name: string;
  voice_category: string;
  text: string;
  date_unix: number;
  character_count_change_from: number;
  character_count_change_to: number;
  content_type: string;
  state: 'created' | 'deleted' | 'processing';
  settings: VoiceSettings;
  feedback?: Feedback;
  share_link_id?: string;
  source: string;
}

export interface Feedback {
  thumbs_up: boolean;
  feedback: string;
  emotions: boolean;
  inaccurate_clone: boolean;
  glitches: boolean;
  audio_quality: boolean;
  other: boolean;
  review_status?: string;
}

// User types
export interface User {
  subscription: Subscription;
  is_new_user: boolean;
  xi_api_key: string;
  can_extend_character_limit: boolean;
  can_extend_voice_limit: boolean;
  can_use_instant_voice_cloning: boolean;
  can_use_professional_voice_cloning: boolean;
  available_models: Model[];
  xi_api_key_preview: string;
}

export interface Subscription {
  tier: string;
  character_count: number;
  character_limit: number;
  can_extend_character_limit: boolean;
  allowed_to_extend_character_limit: boolean;
  next_character_count_reset_unix: number;
  voice_limit: number;
  max_voice_add_edits: number;
  voice_add_edit_counter: number;
  professional_voice_limit: number;
  can_extend_voice_limit: boolean;
  can_use_instant_voice_cloning: boolean;
  can_use_professional_voice_cloning: boolean;
  currency: string;
  status: string;
  billing_period: string;
  character_refresh_period: string;
  next_invoice?: Invoice;
  has_open_invoices: boolean;
}

export interface Invoice {
  amount_due_cents: number;
  next_payment_attempt_unix: number;
}

// Hook types
export interface UseElevenLabsOptions {
  apiKey?: string;
  enableLogging?: boolean;
}

export interface UseElevenLabsReturn {
  voices: Voice[] | null;
  models: Model[] | null;
  user: User | null;
  isLoading: boolean;
  error: ElevenLabsError | null;
  refreshVoices: () => Promise<void>;
  refreshModels: () => Promise<void>;
  refreshUser: () => Promise<void>;
  generateSpeech: (voiceId: string, request: TextToSpeechRequest) => Promise<ArrayBuffer>;
  streamSpeech: (voiceId: string, request: StreamingRequest) => Promise<ReadableStream>;
}

// Configuration types
export interface ElevenLabsConfig {
  apiKey: string;
  baseUrl: string;
  timeout: number;
  retries: number;
}

// Output formats
export type OutputFormat = 
  | 'mp3_22050_32'
  | 'mp3_44100_32'
  | 'mp3_44100_64'
  | 'mp3_44100_96'
  | 'mp3_44100_128'
  | 'mp3_44100_192'
  | 'pcm_16000'
  | 'pcm_22050'
  | 'pcm_24000'
  | 'pcm_44100'
  | 'ulaw_8000';

// Common model IDs
export const ELEVENLABS_MODELS = {
  MULTILINGUAL_V2: 'eleven_multilingual_v2',
  FLASH_V2_5: 'eleven_flash_v2_5',
  TURBO_V2_5: 'eleven_turbo_v2_5',
  ENGLISH_V1: 'eleven_monolingual_v1',
  TURBO_V2: 'eleven_turbo_v2',
} as const;

// TTS Models with metadata (from official template)
export const TTS_MODELS = {
  MULTILINGUAL: 'eleven_multilingual_v2',
  FLASH: 'eleven_flash_v2_5',
} as const;

export const TTS_MODEL_INFO = {
  [TTS_MODELS.MULTILINGUAL]: {
    name: 'High Quality',
    description: 'Superior quality, slower generation',
  },
  [TTS_MODELS.FLASH]: {
    name: 'Flash',
    description: 'Faster generation at 50% off, good quality',
  },
} as const;

// Language codes
export type LanguageCode = 
  | 'en' | 'ja' | 'zh' | 'de' | 'hi' | 'fr' | 'ko' | 'pt' | 'it' | 'es' 
  | 'id' | 'nl' | 'tr' | 'fil' | 'pl' | 'sv' | 'bg' | 'ro' | 'ar' | 'cs' 
  | 'el' | 'fi' | 'hr' | 'ms' | 'sk' | 'da' | 'ta' | 'uk' | 'ru' | 'hu' 
  | 'no' | 'vi';

// Error codes
export enum ElevenLabsErrorCode {
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

// API Response wrappers
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ElevenLabsError;
  timestamp: string;
}

// Utility types
export type VoiceCategory = 'premade' | 'cloned' | 'professional' | 'generated';
export type VoiceTier = 'free' | 'starter' | 'creator' | 'pro' | 'scale' | 'business';
export type ModelType = 'tts' | 'sts' | 'vc';

// Voice cloning types
export interface VoiceCloneRequest {
  name: string;
  description?: string;
  files: File[];
  labels?: Record<string, string>;
}

export interface VoiceCloneResponse {
  voice_id: string;
}

// Pronunciation dictionary types
export interface PronunciationDictionary {
  id: string;
  name: string;
  description?: string;
  created_by: string;
  creation_time_unix: number;
  version_id: string;
}

export interface PronunciationRule {
  type: 'phoneme' | 'alias';
  string_to_replace: string;
  phoneme?: string;
  alias?: string;
  alphabet?: 'ipa' | 'cmu';
}

// Webhook types (for future use)
export interface WebhookEvent {
  type: 'speech.generated' | 'voice.cloned' | 'subscription.updated';
  data: unknown;
  timestamp: string;
  signature: string;
}
