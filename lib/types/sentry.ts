/**
 * Sentry TypeScript Type Definitions
 * Comprehensive types for Sentry error monitoring and performance tracking
 */

import type { User, Breadcrumb, SeverityLevel, Event, EventHint } from '@sentry/nextjs';

// =============================================================================
// Core Sentry Types
// =============================================================================

/**
 * Sentry configuration options
 */
export interface SentryConfig {
  dsn: string;
  environment?: string;
  release?: string;
  tracesSampleRate?: number;
  replaysSessionSampleRate?: number;
  replaysOnErrorSampleRate?: number;
  sendDefaultPii?: boolean;
  debug?: boolean;
  beforeSend?: (event: Event, hint: EventHint) => Event | null;
  beforeSendTransaction?: (event: Event, hint: EventHint) => Event | null;
}

/**
 * Sentry user context
 */
export interface SentryUser extends User {
  id?: string;
  email?: string;
  username?: string;
  ip_address?: string;
  subscription?: {
    plan: string;
    status: string;
  };
  metadata?: Record<string, any>;
}

/**
 * Sentry breadcrumb for tracking user actions
 */
export interface SentryBreadcrumb extends Breadcrumb {
  timestamp?: number;
  type?: string;
  category?: string;
  level?: SeverityLevel;
  message?: string;
  data?: Record<string, any>;
}

/**
 * Sentry tag for categorizing events
 */
export interface SentryTag {
  key: string;
  value: string;
}

/**
 * Sentry context for additional event data
 */
export interface SentryContext {
  user?: SentryUser;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  level?: SeverityLevel;
  fingerprint?: string[];
}

// =============================================================================
// Error Handling Types
// =============================================================================

/**
 * Sentry error with additional context
 */
export interface SentryError extends Error {
  code?: string;
  statusCode?: number;
  context?: Record<string, any>;
  fingerprint?: string[];
  level?: SeverityLevel;
}

/**
 * Error capture options
 */
export interface ErrorCaptureOptions {
  user?: SentryUser;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  level?: SeverityLevel;
  fingerprint?: string[];
  contexts?: Record<string, any>;
}

/**
 * API error context
 */
export interface APIErrorContext {
  endpoint: string;
  method: string;
  statusCode: number;
  requestId?: string;
  userId?: string;
  duration?: number;
  requestBody?: any;
  responseBody?: any;
}

// =============================================================================
// Performance Monitoring Types
// =============================================================================

/**
 * Performance span data
 */
export interface SpanData {
  op: string;
  description: string;
  tags?: Record<string, string>;
  data?: Record<string, any>;
  startTimestamp?: number;
  endTimestamp?: number;
}

/**
 * Transaction context
 */
export interface TransactionContext {
  name: string;
  op: string;
  description?: string;
  tags?: Record<string, string>;
  data?: Record<string, any>;
  sampled?: boolean;
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  duration: number;
  startTime: number;
  endTime: number;
  operation: string;
  description: string;
  success: boolean;
  errorCount?: number;
  tags?: Record<string, string>;
}

// =============================================================================
// Session Replay Types
// =============================================================================

/**
 * Session replay configuration
 */
export interface ReplayConfig {
  sessionSampleRate: number;
  errorSampleRate: number;
  maskAllText?: boolean;
  maskAllInputs?: boolean;
  blockAllMedia?: boolean;
  mutationBreadcrumbLimit?: number;
  mutationLimit?: number;
}

/**
 * Session replay event
 */
export interface ReplayEvent {
  sessionId: string;
  timestamp: number;
  type: 'session' | 'error' | 'navigation' | 'click' | 'input';
  data: Record<string, any>;
}

// =============================================================================
// Hook Types
// =============================================================================

/**
 * Sentry hook options
 */
export interface UseSentryOptions {
  enablePerformanceMonitoring?: boolean;
  enableSessionReplay?: boolean;
  enableUserFeedback?: boolean;
  autoCapture?: boolean;
  defaultTags?: Record<string, string>;
  defaultUser?: SentryUser;
}

/**
 * Sentry hook return type
 */
export interface UseSentryReturn {
  // Error handling
  captureError: (error: Error, context?: ErrorCaptureOptions) => string;
  captureMessage: (message: string, level?: SeverityLevel, context?: ErrorCaptureOptions) => string;
  captureException: (exception: any, context?: ErrorCaptureOptions) => string;
  
  // User context
  setUser: (user: SentryUser) => void;
  clearUser: () => void;
  
  // Tags and context
  setTag: (key: string, value: string) => void;
  setTags: (tags: Record<string, string>) => void;
  setContext: (key: string, context: any) => void;
  setExtra: (key: string, extra: any) => void;
  
  // Breadcrumbs
  addBreadcrumb: (breadcrumb: SentryBreadcrumb) => void;
  
  // Performance monitoring
  startTransaction: (context: TransactionContext) => any;
  startSpan: (spanData: SpanData) => Promise<any>;
  
  // Session replay
  captureReplay: () => void;
  
  // User feedback
  showUserFeedback: () => void;
  
  // Utilities
  isEnabled: boolean;
  lastEventId: string | null;
}

// =============================================================================
// API Response Types
// =============================================================================

/**
 * Sentry API error response
 */
export interface SentryAPIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  eventId?: string;
}

/**
 * Sentry health check response
 */
export interface SentryHealthCheck {
  status: 'healthy' | 'unhealthy';
  dsn: string;
  environment: string;
  release?: string;
  lastEventId?: string;
  timestamp: string;
}

// =============================================================================
// Utility Types
// =============================================================================

/**
 * Sentry environment type
 */
export type SentryEnvironment = 'development' | 'staging' | 'production' | 'test';

/**
 * Sentry severity levels
 */
export type SentrySeverity = 'fatal' | 'error' | 'warning' | 'info' | 'debug';

/**
 * Sentry operation types for performance monitoring
 */
export type SentryOperation = 
  | 'navigation'
  | 'pageload'
  | 'http.client'
  | 'http.server'
  | 'db.query'
  | 'cache.get'
  | 'cache.set'
  | 'ui.click'
  | 'ui.input'
  | 'function'
  | 'custom';

/**
 * Sentry integration names
 */
export type SentryIntegration = 
  | 'InboundFilters'
  | 'FunctionToString'
  | 'TryCatch'
  | 'Breadcrumbs'
  | 'GlobalHandlers'
  | 'LinkedErrors'
  | 'Dedupe'
  | 'HttpContext'
  | 'Replay'
  | 'BrowserTracing';

// =============================================================================
// Export all types
// =============================================================================

export type {
  Event,
  EventHint,
  User,
  Breadcrumb,
  SeverityLevel,
};
