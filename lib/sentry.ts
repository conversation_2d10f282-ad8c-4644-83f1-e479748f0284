/**
 * Sentry Utility Functions
 * Helper functions for Sentry error monitoring and performance tracking
 */

import * as Sentry from '@sentry/nextjs';
import type {
  SentryUser,
  SentryError,
  ErrorCaptureOptions,
  APIErrorContext,
  SpanData,
  TransactionContext,
  PerformanceMetrics,
  SentryBreadcrumb,
  SentryEnvironment,
  SentrySeverity,
  SentryOperation,
} from '@/lib/types/sentry';

// =============================================================================
// Configuration Utilities
// =============================================================================

/**
 * Check if Sentry is enabled and properly configured
 */
export function isSentryEnabled(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_SENTRY_DSN &&
    process.env.FEATURE_SENTRY !== 'false'
  );
}

/**
 * Get Sentry environment from environment variables
 */
export function getSentryEnvironment(): SentryEnvironment {
  const env = process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV;
  
  switch (env) {
    case 'production':
      return 'production';
    case 'staging':
      return 'staging';
    case 'test':
      return 'test';
    default:
      return 'development';
  }
}

/**
 * Get Sentry release version
 */
export function getSentryRelease(): string | undefined {
  return process.env.SENTRY_RELEASE || process.env.npm_package_version;
}

// =============================================================================
// Error Handling Utilities
// =============================================================================

/**
 * Capture an error with additional context
 */
export function captureError(
  error: Error | SentryError,
  context?: ErrorCaptureOptions
): string {
  if (!isSentryEnabled()) {
    console.error('Sentry Error:', error);
    return '';
  }

  return Sentry.withScope((scope) => {
    // Set user context
    if (context?.user) {
      scope.setUser(context.user);
    }

    // Set tags
    if (context?.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        scope.setTag(key, value);
      });
    }

    // Set extra data
    if (context?.extra) {
      Object.entries(context.extra).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
    }

    // Set contexts
    if (context?.contexts) {
      Object.entries(context.contexts).forEach(([key, value]) => {
        scope.setContext(key, value);
      });
    }

    // Set level
    if (context?.level) {
      scope.setLevel(context.level);
    }

    // Set fingerprint
    if (context?.fingerprint) {
      scope.setFingerprint(context.fingerprint);
    }

    return Sentry.captureException(error);
  });
}

/**
 * Capture a message with context
 */
export function captureMessage(
  message: string,
  level: SentrySeverity = 'info',
  context?: ErrorCaptureOptions
): string {
  if (!isSentryEnabled()) {
    console.log(`Sentry Message [${level}]:`, message);
    return '';
  }

  return Sentry.withScope((scope) => {
    if (context?.user) scope.setUser(context.user);
    if (context?.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        scope.setTag(key, value);
      });
    }
    if (context?.extra) {
      Object.entries(context.extra).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
    }

    scope.setLevel(level);
    return Sentry.captureMessage(message);
  });
}

/**
 * Capture API error with request/response context
 */
export function captureAPIError(
  error: Error,
  context: APIErrorContext,
  user?: SentryUser
): string {
  const errorContext: ErrorCaptureOptions = {
    user,
    tags: {
      endpoint: context.endpoint,
      method: context.method,
      statusCode: context.statusCode.toString(),
    },
    extra: {
      requestId: context.requestId,
      duration: context.duration,
      requestBody: context.requestBody,
      responseBody: context.responseBody,
    },
    contexts: {
      api: {
        endpoint: context.endpoint,
        method: context.method,
        statusCode: context.statusCode,
        duration: context.duration,
      },
    },
    fingerprint: [
      'api-error',
      context.endpoint,
      context.method,
      context.statusCode.toString(),
    ],
  };

  return captureError(error, errorContext);
}

// =============================================================================
// User Context Utilities
// =============================================================================

/**
 * Set user context for Sentry
 */
export function setUser(user: SentryUser): void {
  if (!isSentryEnabled()) return;
  
  Sentry.setUser(user);
}

/**
 * Clear user context
 */
export function clearUser(): void {
  if (!isSentryEnabled()) return;
  
  Sentry.setUser(null);
}

/**
 * Set user context with subscription information
 */
export function setUserWithSubscription(
  user: Partial<SentryUser>,
  subscription?: { plan: string; status: string }
): void {
  const sentryUser: SentryUser = {
    ...user,
    subscription,
  };
  
  setUser(sentryUser);
}

// =============================================================================
// Performance Monitoring Utilities
// =============================================================================

/**
 * Start a performance transaction
 */
export function startTransaction(context: TransactionContext): any {
  if (!isSentryEnabled()) {
    return {
      setTag: () => {},
      setData: () => {},
      finish: () => {},
    };
  }

  return Sentry.startTransaction(context);
}

/**
 * Start a performance span
 */
export async function startSpan<T>(
  spanData: SpanData,
  callback: () => Promise<T> | T
): Promise<T> {
  if (!isSentryEnabled()) {
    return await callback();
  }

  return await Sentry.startSpan(
    {
      op: spanData.op as any,
      name: spanData.description,
      data: spanData.data,
    },
    callback
  );
}

/**
 * Measure function performance
 */
export async function measurePerformance<T>(
  operation: SentryOperation,
  description: string,
  callback: () => Promise<T> | T,
  tags?: Record<string, string>
): Promise<{ result: T; metrics: PerformanceMetrics }> {
  const startTime = Date.now();
  let success = true;
  let errorCount = 0;

  try {
    const result = await startSpan(
      {
        op: operation,
        description,
        tags,
        startTimestamp: startTime / 1000,
      },
      callback
    );

    const endTime = Date.now();
    const metrics: PerformanceMetrics = {
      duration: endTime - startTime,
      startTime,
      endTime,
      operation,
      description,
      success,
      errorCount,
      tags,
    };

    return { result, metrics };
  } catch (error) {
    success = false;
    errorCount = 1;
    
    const endTime = Date.now();
    const metrics: PerformanceMetrics = {
      duration: endTime - startTime,
      startTime,
      endTime,
      operation,
      description,
      success,
      errorCount,
      tags,
    };

    // Capture the error
    captureError(error as Error, {
      tags: {
        operation,
        ...tags,
      },
      extra: {
        duration: metrics.duration,
        description,
      },
    });

    throw error;
  }
}

// =============================================================================
// Breadcrumb Utilities
// =============================================================================

/**
 * Add a breadcrumb for user actions
 */
export function addBreadcrumb(breadcrumb: SentryBreadcrumb): void {
  if (!isSentryEnabled()) return;
  
  Sentry.addBreadcrumb(breadcrumb);
}

/**
 * Add navigation breadcrumb
 */
export function addNavigationBreadcrumb(from: string, to: string): void {
  addBreadcrumb({
    category: 'navigation',
    message: `Navigated from ${from} to ${to}`,
    level: 'info',
    data: { from, to },
  });
}

/**
 * Add API call breadcrumb
 */
export function addAPIBreadcrumb(
  method: string,
  url: string,
  statusCode?: number,
  duration?: number
): void {
  addBreadcrumb({
    category: 'http',
    message: `${method} ${url}`,
    level: statusCode && statusCode >= 400 ? 'error' : 'info',
    data: {
      method,
      url,
      statusCode,
      duration,
    },
  });
}

/**
 * Add user action breadcrumb
 */
export function addUserActionBreadcrumb(
  action: string,
  target?: string,
  data?: Record<string, any>
): void {
  addBreadcrumb({
    category: 'user',
    message: `User ${action}${target ? ` on ${target}` : ''}`,
    level: 'info',
    data: {
      action,
      target,
      ...data,
    },
  });
}

// =============================================================================
// Tag and Context Utilities
// =============================================================================

/**
 * Set a tag for all subsequent events
 */
export function setTag(key: string, value: string): void {
  if (!isSentryEnabled()) return;
  
  Sentry.setTag(key, value);
}

/**
 * Set multiple tags
 */
export function setTags(tags: Record<string, string>): void {
  if (!isSentryEnabled()) return;
  
  Object.entries(tags).forEach(([key, value]) => {
    Sentry.setTag(key, value);
  });
}

/**
 * Set context data
 */
export function setContext(key: string, context: any): void {
  if (!isSentryEnabled()) return;
  
  Sentry.setContext(key, context);
}

/**
 * Set extra data
 */
export function setExtra(key: string, extra: any): void {
  if (!isSentryEnabled()) return;
  
  Sentry.setExtra(key, extra);
}

// =============================================================================
// Session Utilities
// =============================================================================

/**
 * Capture current session replay
 */
export function captureReplay(): void {
  if (!isSentryEnabled()) return;
  
  // Force capture current replay session
  const replay = Sentry.getReplay();
  if (replay) {
    replay.flush();
  }
}

/**
 * Get current session ID
 */
export function getSessionId(): string | undefined {
  if (!isSentryEnabled()) return undefined;
  
  const replay = Sentry.getReplay();
  return replay?.getSessionId();
}

// =============================================================================
// Health Check Utilities
// =============================================================================

/**
 * Test Sentry connection
 */
export async function testSentryConnection(): Promise<boolean> {
  if (!isSentryEnabled()) return false;
  
  try {
    const eventId = captureMessage('Sentry connection test', 'info');
    return !!eventId;
  } catch (error) {
    console.error('Sentry connection test failed:', error);
    return false;
  }
}

/**
 * Get last event ID
 */
export function getLastEventId(): string | null {
  if (!isSentryEnabled()) return null;
  
  return Sentry.lastEventId();
}
