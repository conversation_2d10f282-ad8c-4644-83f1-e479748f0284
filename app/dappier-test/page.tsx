/**
 * Dappier Test Page
 * Test page for verifying Dappier AI search and copilot functionality
 */

'use client';

import { useState } from 'react';
import { useDappier, useDappierQuery, useDappierSearch, useDappierRecommendations } from '@/hooks/useDappier';
import { SearchBar } from '@/components/dappier/search-bar';
import { CopilotChat } from '@/components/dappier/copilot-chat';
import { Recommendations } from '@/components/dappier/recommendations';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  MessageSquare, 
  TrendingUp, 
  Sparkles,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  Bot,
  Database,
  Target
} from 'lucide-react';

export default function DappierTestPage() {
  const { queryRealTimeData, search, getRecommendations, sendMessage, isLoading, error, isEnabled } = useDappier();
  const { query: quickQuery } = useDappierQuery();
  const { search: quickSearch } = useDappierSearch();
  const { recommend } = useDappierRecommendations();

  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'pending'>>({});
  const [testQuery, setTestQuery] = useState('What is the current weather in New York?');
  const [searchQuery, setSearchQuery] = useState('artificial intelligence trends');
  const [copilotMessage, setCopilotMessage] = useState('Hello, how can you help me?');
  const [userId, setUserId] = useState('test-user-123');
  const [responses, setResponses] = useState<Record<string, any>>({});

  const updateTestResult = (test: string, result: 'success' | 'error' | 'pending') => {
    setTestResults(prev => ({ ...prev, [test]: result }));
  };

  const updateResponse = (test: string, response: any) => {
    setResponses(prev => ({ ...prev, [test]: response }));
  };

  // Test Functions
  const testRealTimeQuery = async () => {
    updateTestResult('realtime', 'pending');
    try {
      const response = await queryRealTimeData({
        query: testQuery,
        includeMetadata: true,
      });
      
      updateResponse('realtime', response);
      updateTestResult('realtime', 'success');
    } catch (err) {
      updateTestResult('realtime', 'error');
      updateResponse('realtime', { error: err });
    }
  };

  const testSearch = async () => {
    updateTestResult('search', 'pending');
    try {
      const response = await search({
        query: searchQuery,
        type: 'semantic',
        includeSnippets: true,
        includeMetadata: true,
        pagination: { page: 1, limit: 5 },
      });
      
      updateResponse('search', response);
      updateTestResult('search', 'success');
    } catch (err) {
      updateTestResult('search', 'error');
      updateResponse('search', { error: err });
    }
  };

  const testRecommendations = async () => {
    updateTestResult('recommendations', 'pending');
    try {
      const response = await getRecommendations({
        userId,
        type: 'content',
        maxResults: 5,
        includeReasons: true,
      });
      
      updateResponse('recommendations', response);
      updateTestResult('recommendations', 'success');
    } catch (err) {
      updateTestResult('recommendations', 'error');
      updateResponse('recommendations', { error: err });
    }
  };

  const testCopilot = async () => {
    updateTestResult('copilot', 'pending');
    try {
      const response = await sendMessage({
        message: copilotMessage,
        personality: {
          tone: 'friendly',
          responseStyle: 'conversational',
        },
        capabilities: [
          { type: 'search', name: 'Web Search', enabled: true },
          { type: 'recommendation', name: 'Recommendations', enabled: true },
        ],
      });
      
      updateResponse('copilot', response);
      updateTestResult('copilot', 'success');
    } catch (err) {
      updateTestResult('copilot', 'error');
      updateResponse('copilot', { error: err });
    }
  };

  const testAPIHealth = async () => {
    updateTestResult('health', 'pending');
    try {
      const response = await fetch('/api/dappier?action=test');
      const data = await response.json();
      
      updateResponse('health', data);
      updateTestResult('health', response.ok ? 'success' : 'error');
    } catch (err) {
      updateTestResult('health', 'error');
      updateResponse('health', { error: err });
    }
  };

  const getStatusIcon = (status: 'success' | 'error' | 'pending' | undefined) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: 'success' | 'error' | 'pending' | undefined) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">Not Run</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Dappier Integration Test</h1>
          <p className="text-muted-foreground">
            Test AI search, custom copilots, and real-time data functionality
          </p>
          
          {/* Status */}
          <div className="flex items-center justify-center gap-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span className="text-sm">
                Dappier Status: {isEnabled ? (
                  <Badge variant="default" className="bg-green-500">Enabled</Badge>
                ) : (
                  <Badge variant="destructive">Disabled</Badge>
                )}
              </span>
            </div>
            
            {error && (
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-500">
                  Error: {error.message}
                </span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Test Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Test Configuration
            </CardTitle>
            <CardDescription>
              Configure test parameters before running tests
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="testQuery">Real-Time Query</Label>
                <Input
                  id="testQuery"
                  value={testQuery}
                  onChange={(e) => setTestQuery(e.target.value)}
                  placeholder="What is the current weather in New York?"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="searchQuery">Search Query</Label>
                <Input
                  id="searchQuery"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="artificial intelligence trends"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="copilotMessage">Copilot Message</Label>
                <Input
                  id="copilotMessage"
                  value={copilotMessage}
                  onChange={(e) => setCopilotMessage(e.target.value)}
                  placeholder="Hello, how can you help me?"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  placeholder="test-user-123"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              API Tests
            </CardTitle>
            <CardDescription>
              Test core Dappier API functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.health)}
                  <span className="text-sm">API Health</span>
                </div>
                <Button size="sm" onClick={testAPIHealth}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.realtime)}
                  <span className="text-sm">Real-Time Data</span>
                </div>
                <Button size="sm" onClick={testRealTimeQuery}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.search)}
                  <span className="text-sm">AI Search</span>
                </div>
                <Button size="sm" onClick={testSearch}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.recommendations)}
                  <span className="text-sm">Recommendations</span>
                </div>
                <Button size="sm" onClick={testRecommendations}>
                  Test
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(testResults.copilot)}
                  <span className="text-sm">Copilot</span>
                </div>
                <Button size="sm" onClick={testCopilot}>
                  Test
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interactive Components */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Interactive Components
            </CardTitle>
            <CardDescription>
              Test the UI components with live functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="search" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="search" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Search
                </TabsTrigger>
                <TabsTrigger value="copilot" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Copilot
                </TabsTrigger>
                <TabsTrigger value="recommendations" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Recommendations
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="search" className="space-y-4">
                <div className="space-y-2">
                  <Label>AI-Powered Search</Label>
                  <SearchBar
                    placeholder="Search with AI..."
                    showSuggestions={true}
                    maxResults={10}
                    onResultSelect={(result) => {
                      console.log('Selected result:', result);
                    }}
                    onSearch={(query, results) => {
                      console.log('Search performed:', query, results);
                    }}
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="copilot" className="space-y-4">
                <div className="space-y-2">
                  <Label>Custom AI Copilot</Label>
                  <CopilotChat
                    title="AI Assistant"
                    personality={{
                      tone: 'friendly',
                      responseStyle: 'conversational',
                    }}
                    capabilities={[
                      { type: 'search', name: 'Web Search', enabled: true },
                      { type: 'recommendation', name: 'Recommendations', enabled: true },
                    ]}
                    maxHeight="400px"
                    onMessageSent={(message) => {
                      console.log('Message sent:', message);
                    }}
                    onResponseReceived={(response) => {
                      console.log('Response received:', response);
                    }}
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="recommendations" className="space-y-4">
                <div className="space-y-2">
                  <Label>AI Recommendations</Label>
                  <Recommendations
                    userId={userId}
                    type="content"
                    maxResults={5}
                    showReasons={true}
                    autoRefresh={false}
                    onRecommendationClick={(recommendation) => {
                      console.log('Recommendation clicked:', recommendation);
                    }}
                    onRecommendationFeedback={(id, feedback) => {
                      console.log('Feedback given:', id, feedback);
                    }}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results Summary</CardTitle>
            <CardDescription>
              Overview of all test results and responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Status Grid */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {Object.entries(testResults).map(([test, status]) => (
                  <div key={test} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm capitalize">{test}</span>
                    {getStatusBadge(status)}
                  </div>
                ))}
              </div>

              {/* Response Details */}
              {Object.keys(responses).length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">Response Details</h4>
                  {Object.entries(responses).map(([test, response]) => (
                    <div key={test} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{test}</Badge>
                        {getStatusIcon(testResults[test])}
                      </div>
                      <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-32">
                        {JSON.stringify(response, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-muted-foreground">
            <p>1. Configure your test parameters above</p>
            <p>2. Run API tests to verify backend functionality</p>
            <p>3. Test interactive components to verify UI integration</p>
            <p>4. Check the browser console for detailed logs</p>
            <p>5. Verify that responses contain expected data structures</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
