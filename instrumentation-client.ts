/**
 * Sentry Client Configuration
 * Client-side Sentry initialization for Next.js App Router
 */

import * as Sentry from '@sentry/nextjs';
import { getSentryEnvironment, getSentryRelease } from '@/lib/sentry';

// Initialize Sentry for client-side operations
Sentry.init({
  // Data Source Name (DSN) - tells <PERSON><PERSON> where to send events
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Environment (development, staging, production)
  environment: getSentryEnvironment(),

  // Release version for tracking deployments
  release: getSentryRelease(),

  // Performance Monitoring
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
  // We recommend adjusting this value in production
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '1.0'),

  // Session Replay
  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: parseFloat(process.env.SENTRY_REPLAYS_SESSION_SAMPLE_RATE || '0.1'),
  replaysOnErrorSampleRate: parseFloat(process.env.SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE || '1.0'),

  // Client-side integrations
  integrations: [
    // Session Replay integration
    Sentry.replayIntegration({
      // Mask all text content for privacy
      maskAllText: false,
      
      // Mask all inputs for privacy
      maskAllInputs: true,
      
      // Block all media (images, videos, etc.)
      blockAllMedia: false,
      
      // Mutation breadcrumb limit
      mutationBreadcrumbLimit: 750,
      
      // Mutation limit
      mutationLimit: 10000,
    }),

    // Browser tracing for performance monitoring
    Sentry.browserTracingIntegration({
      // Capture interactions (clicks, navigation, etc.)
      enableInp: true,
      
      // Capture long tasks
      enableLongTask: true,
      
      // Router instrumentation
      routingInstrumentation: Sentry.reactRouterV6Instrumentation(
        // React Router integration would go here if using React Router
        // For Next.js App Router, this is handled automatically
      ),
    }),

    // HTTP integration for tracking outgoing requests
    Sentry.httpIntegration({
      tracing: {
        // Capture outgoing HTTP requests
        shouldCreateSpanForRequest: (url) => {
          // Don't trace Sentry requests to avoid infinite loops
          return !url.includes('sentry.io');
        },
      },
    }),

    // Breadcrumbs integration
    Sentry.breadcrumbsIntegration({
      // Capture console logs as breadcrumbs
      console: true,
      
      // Capture DOM events as breadcrumbs
      dom: true,
      
      // Capture fetch/XHR requests as breadcrumbs
      fetch: true,
      
      // Capture history changes as breadcrumbs
      history: true,
      
      // Capture SentrySDK calls as breadcrumbs
      sentry: true,
    }),

    // Global error handlers
    Sentry.globalHandlersIntegration({
      onerror: true,
      onunhandledrejection: true,
    }),
  ],

  // Debug mode (only in development)
  debug: process.env.SENTRY_DEBUG === 'true' || process.env.NODE_ENV === 'development',

  // Send default PII (Personally Identifiable Information)
  // Set to true to include user IP, cookies, etc.
  sendDefaultPii: process.env.SENTRY_SEND_DEFAULT_PII === 'true',

  // Maximum breadcrumbs to keep
  maxBreadcrumbs: 100,

  // Attach stack traces to pure capture message calls
  attachStacktrace: true,

  // Auto session tracking
  autoSessionTracking: true,

  // Before send hook - filter or modify events before sending
  beforeSend(event, hint) {
    // Don't send events in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out specific errors
    if (event.exception) {
      const error = hint.originalException;
      
      // Filter out network errors that are not actionable
      if (error instanceof Error) {
        if (
          error.message.includes('Network request failed') ||
          error.message.includes('ChunkLoadError') ||
          error.message.includes('Loading chunk') ||
          error.message.includes('AbortError')
        ) {
          return null;
        }
      }
    }

    // Add client-specific context
    event.contexts = {
      ...event.contexts,
      browser: {
        name: navigator.userAgent,
        version: navigator.appVersion,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,
      },
    };

    return event;
  },

  // Before send transaction hook
  beforeSendTransaction(event) {
    // Don't send transactions in test environment
    if (process.env.NODE_ENV === 'test') {
      return null;
    }

    // Filter out health check transactions
    if (event.transaction?.includes('/api/health')) {
      return null;
    }

    return event;
  },

  // Error filtering
  ignoreErrors: [
    // Browser extensions
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
    'Script error.',
    
    // Network errors
    'NetworkError',
    'Failed to fetch',
    'Load failed',
    
    // Cancelled requests
    'AbortError',
    'The operation was aborted',
    
    // Common browser errors that aren't actionable
    'ChunkLoadError',
    'Loading chunk',
    'Loading CSS chunk',
    
    // React development warnings
    'Warning: ',
    
    // Browser-specific errors
    'Non-Error exception captured',
    'Object Not Found Matching Id',
  ],

  // Ignore specific URLs
  denyUrls: [
    // Browser extensions
    /extensions\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
    
    // Ad blockers
    /adnxs\.com/i,
    /adsystem\.google\.com/i,
    
    // Social media widgets
    /connect\.facebook\.net/i,
    /platform\.twitter\.com/i,
    
    // Analytics
    /google-analytics\.com/i,
    /googletagmanager\.com/i,
  ],

  // Initial scope configuration
  initialScope: {
    tags: {
      component: 'client',
      runtime: 'browser',
    },
  },
});

// Export router transition capture for Next.js App Router
// This export will instrument router navigations, and is only relevant if you enable tracing
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;

// Log initialization in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Sentry client-side initialized:', {
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN ? '***configured***' : 'missing',
    environment: getSentryEnvironment(),
    release: getSentryRelease(),
    debug: process.env.SENTRY_DEBUG === 'true',
    replaysEnabled: parseFloat(process.env.SENTRY_REPLAYS_SESSION_SAMPLE_RATE || '0.1') > 0,
  });
}
