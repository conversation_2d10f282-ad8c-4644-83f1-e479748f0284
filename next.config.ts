import type { NextConfig } from "next";
import { withSentryConfig } from "@sentry/nextjs";

const nextConfig: NextConfig = {
  // Environment variables configuration
  env: {
    // Public environment variables (exposed to the client)
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY: process.env.NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY,
    NEXT_PUBLIC_REVENUECAT_ENVIRONMENT: process.env.NEXT_PUBLIC_REVENUECAT_ENVIRONMENT,
    NEXT_PUBLIC_ELEVENLABS_API_KEY: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  },

  // Server-side environment variables validation
  serverExternalPackages: ['@revenuecat/purchases-js', '@elevenlabs/elevenlabs-js', '@sentry/nextjs'],

  // API routes configuration
  async rewrites() {
    return [
      {
        source: '/api/health',
        destination: '/api/health',
      },
    ];
  },

  // Headers for security and CORS
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.CORS_ORIGIN || '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },

  // Webpack configuration for external packages
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Client-side configuration
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Standard webpack watch options
    config.watchOptions = {
      ...config.watchOptions,
      ignored: ['**/node_modules/**'],
    };

    return config;
  },

  // Image optimization configuration
  images: {
    domains: [
      // Add domains for external images if needed
      'api.revenuecat.com',
      'cdn.revenuecat.com',
    ],
  },

  // TypeScript configuration
  typescript: {
    // Type checking is handled by the build process
    ignoreBuildErrors: false,
  },

  // ESLint configuration
  eslint: {
    // ESLint is handled separately
    ignoreDuringBuilds: false,
  },

  // Compression and optimization
  compress: true,
  poweredByHeader: false,

  // Redirects for API documentation
  async redirects() {
    return [
      {
        source: '/docs/api',
        destination: '/api-docs',
        permanent: true,
      },
    ];
  },
};

// Sentry configuration options
const sentryWebpackPluginOptions = {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically instrument Next.js data fetching methods and API routes
  automaticVercelMonitors: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers
  // This can increase your server load as well as your hosting bill
  // Note: Check that the configured route will not match with your Next.js middleware
  tunnelRoute: "/monitoring",

  // Capture React component names for better debugging
  reactComponentAnnotation: {
    enabled: true,
  },
};

// Make sure adding Sentry options is the last code to run before exporting
export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);
