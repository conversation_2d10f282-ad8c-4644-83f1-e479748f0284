/**
 * ElevenLabs Integration Example Component
 * Demonstrates how to use the ElevenLabs hooks and SDK integration
 */

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';
import { 
  Loader2, 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  Mic, 
  Download,
  RefreshCw 
} from 'lucide-react';
import {
  useElevenLabs,
  useTextToSpeech,
  useVoiceManagement,
  useAudioPlayer,
} from '@/hooks/useElevenLabs';
import { ELEVENLABS_MODELS } from '@/lib/types/elevenlabs';

export function ElevenLabsExample() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [selectedVoiceId, setSelectedVoiceId] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>(ELEVENLABS_MODELS.MULTILINGUAL_V2);
  const [textToSpeak, setTextToSpeak] = useState('Hello! This is a test of ElevenLabs text-to-speech technology.');
  const [stability, setStability] = useState([0.5]);
  const [similarityBoost, setSimilarityBoost] = useState([0.75]);

  // Main ElevenLabs hook
  const {
    voices,
    user,
    isLoading: isMainLoading,
    error: mainError,
    refreshVoices,
    refreshUser,
  } = useElevenLabs({
    enableLogging: true,
  });

  // Text-to-speech hook
  const {
    generateAndPlay,
    clearAudio,
    isGenerating,
    error: ttsError,
    audioUrl,
  } = useTextToSpeech();

  // Voice management hook
  const {
    isLoading: isVoiceLoading,
    error: voiceError,
  } = useVoiceManagement();

  // Audio player hook
  const {
    play,
    pause,
    stop,
    seek,
    isPlaying,
    currentTime,
    duration,
  } = useAudioPlayer();

  const handleConnect = () => {
    setIsEnabled(true);
  };

  const handleDisconnect = () => {
    setIsEnabled(false);
    clearAudio();
    stop();
  };

  const handleGenerateSpeech = async () => {
    if (!selectedVoiceId || !textToSpeak.trim()) {
      alert('Please select a voice and enter text to speak');
      return;
    }

    try {
      const audioUrl = await generateAndPlay(selectedVoiceId, textToSpeak, {
        model_id: selectedModel,
        voice_settings: {
          stability: stability[0],
          similarity_boost: similarityBoost[0],
        },
      });

      // Auto-play the generated audio
      await play(audioUrl);
    } catch (error) {
      console.error('Failed to generate speech:', error);
    }
  };

  const handleDownloadAudio = () => {
    if (audioUrl) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = `elevenlabs-speech-${Date.now()}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const isLoading = isMainLoading || isGenerating || isVoiceLoading;
  const error = mainError || ttsError || voiceError;

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">ElevenLabs Integration Example</h1>
        <p className="text-muted-foreground">
          Demonstrates the ElevenLabs text-to-speech integration with real API calls
        </p>
      </div>

      {/* Connection Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Connection Settings
          </CardTitle>
          <CardDescription>
            Connect to ElevenLabs to access text-to-speech functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-center">
            {!isEnabled ? (
              <Button onClick={handleConnect}>
                Connect to ElevenLabs
              </Button>
            ) : (
              <Button onClick={handleDisconnect} variant="outline">
                Disconnect
              </Button>
            )}
            {isEnabled && (
              <Badge variant="secondary">Connected</Badge>
            )}
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            <strong>Error:</strong> {error.message}
          </AlertDescription>
        </Alert>
      )}

      {/* User Information */}
      {isEnabled && user && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Account Information</span>
              <Button variant="outline" size="sm" onClick={refreshUser} disabled={isLoading}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Subscription Tier:</strong> {user.subscription.tier}
              </div>
              <div>
                <strong>Character Limit:</strong> {user.subscription.character_limit.toLocaleString()}
              </div>
              <div>
                <strong>Characters Used:</strong> {user.subscription.character_count.toLocaleString()}
              </div>
              <div>
                <strong>Voice Limit:</strong> {user.subscription.voice_limit}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Text-to-Speech Interface */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle>Text-to-Speech Generator</CardTitle>
            <CardDescription>
              Generate speech from text using ElevenLabs voices
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Voice Selection */}
            <div className="space-y-2">
              <Label htmlFor="voice-select">Select Voice</Label>
              <Select value={selectedVoiceId} onValueChange={setSelectedVoiceId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a voice..." />
                </SelectTrigger>
                <SelectContent>
                  {voices?.map((voice) => (
                    <SelectItem key={voice.voice_id} value={voice.voice_id}>
                      {voice.name} ({voice.category})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Model Selection */}
            <div className="space-y-2">
              <Label htmlFor="model-select">Select Model</Label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ELEVENLABS_MODELS.MULTILINGUAL_V2}>
                    Multilingual v2 (Recommended)
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_MODELS.FLASH_V2_5}>
                    Flash v2.5 (Ultra-low latency)
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_MODELS.TURBO_V2_5}>
                    Turbo v2.5 (Balanced)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Text Input */}
            <div className="space-y-2">
              <Label htmlFor="text-input">Text to Speak</Label>
              <Textarea
                id="text-input"
                value={textToSpeak}
                onChange={(e) => setTextToSpeak(e.target.value)}
                placeholder="Enter the text you want to convert to speech..."
                rows={3}
              />
              <div className="text-sm text-muted-foreground">
                Characters: {textToSpeak.length}
              </div>
            </div>

            {/* Voice Settings */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Stability: {stability[0]}</Label>
                <Slider
                  value={stability}
                  onValueChange={setStability}
                  max={1}
                  min={0}
                  step={0.01}
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label>Similarity Boost: {similarityBoost[0]}</Label>
                <Slider
                  value={similarityBoost}
                  onValueChange={setSimilarityBoost}
                  max={1}
                  min={0}
                  step={0.01}
                  className="w-full"
                />
              </div>
            </div>

            {/* Generate Button */}
            <Button 
              onClick={handleGenerateSpeech} 
              disabled={isGenerating || !selectedVoiceId || !textToSpeak.trim()}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Generating Speech...
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Generate Speech
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Audio Player */}
      {audioUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Audio Player</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => isPlaying ? pause() : play(audioUrl)}
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="outline" size="sm" onClick={stop}>
                <Square className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownloadAudio}>
                <Download className="h-4 w-4" />
              </Button>
              <div className="flex-1 text-center text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            
            {duration > 0 && (
              <div className="space-y-2">
                <Slider
                  value={[currentTime]}
                  onValueChange={([value]) => seek(value)}
                  max={duration}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Available Voices */}
      {isEnabled && voices && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Available Voices ({voices.length})</span>
              <Button variant="outline" size="sm" onClick={refreshVoices} disabled={isLoading}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {voices.slice(0, 9).map((voice) => (
                <div key={voice.voice_id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">{voice.name}</h4>
                    <Badge variant="secondary" className="text-xs">
                      {voice.category}
                    </Badge>
                  </div>
                  {voice.description && (
                    <p className="text-sm text-muted-foreground mb-2">
                      {voice.description}
                    </p>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedVoiceId(voice.voice_id)}
                    className="w-full"
                  >
                    Select Voice
                  </Button>
                </div>
              ))}
            </div>
            {voices.length > 9 && (
              <p className="text-sm text-muted-foreground mt-4 text-center">
                Showing 9 of {voices.length} voices
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
