/**
 * ElevenLabs API Route Handler
 * Handles server-side ElevenLabs operations (primarily for admin/webhook operations)
 * Most operations are handled client-side via the SDK
 */

import { NextRequest } from 'next/server';
import { z } from 'zod';
import {
  createSuccessResponse,
  createErrorResponse,
  validateMethod,
  validateEnvVars,
  extractJsonBody,
  extractQueryParams,
  withErrorHandling,
} from '@/lib/apiResponse';

// Environment validation
const requiredEnvVars = {
  ELEVENLABS_API_KEY: process.env.ELEVENLABS_API_KEY,
};

// Validation schemas
const getUserUsageSchema = z.object({
  startUnix: z.number().optional(),
  endUnix: z.number().optional(),
});

const deleteHistoryItemSchema = z.object({
  history_item_id: z.string().min(1, 'History item ID is required'),
});

const downloadHistoryItemSchema = z.object({
  history_item_id: z.string().min(1, 'History item ID is required'),
});

// ElevenLabs API configuration
const ELEVENLABS_API_BASE = 'https://api.elevenlabs.io/v1';

// API request helper for server-side operations
async function makeElevenLabsRequest(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const missing = validateEnvVars(requiredEnvVars);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  const response = await fetch(`${ELEVENLABS_API_BASE}${endpoint}`, {
    ...options,
    headers: {
      'xi-api-key': requiredEnvVars.ELEVENLABS_API_KEY!,
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      status: response.status,
      message: errorData.detail?.message || response.statusText,
      detail: errorData.detail,
    };
  }

  return response;
}

/**
 * GET /api/elevenlabs
 * Handles server-side GET operations
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  const methodError = validateMethod(request, ['GET']);
  if (methodError) return methodError;

  const params = extractQueryParams(request.url);
  const { action } = params;

  if (!action) {
    return createErrorResponse({
      code: 'MISSING_ACTION',
      message: 'Action parameter is required. Supported actions: user-usage, history, download-history-item',
      timestamp: new Date().toISOString(),
    }, 400);
  }

  switch (action) {
    case 'user-usage':
      return await getUserUsage(params);

    case 'history':
      return await getHistory(params);

    case 'download-history-item':
      return await downloadHistoryItem(params);
    
    default:
      return createErrorResponse({
        code: 'INVALID_ACTION',
        message: `Unsupported action: ${action}. Supported actions: user-usage, history, download-history-item`,
        timestamp: new Date().toISOString(),
      }, 400);
  }
});

/**
 * POST /api/elevenlabs
 * Handles server-side POST operations
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  const methodError = validateMethod(request, ['POST']);
  if (methodError) return methodError;

  const body = await extractJsonBody<{ action?: string }>(request);
  const { action } = body;

  if (!action) {
    return createErrorResponse({
      code: 'MISSING_ACTION',
      message: 'Action field is required in request body. Supported actions: delete-history-item',
      timestamp: new Date().toISOString(),
    }, 400);
  }

  switch (action) {
    case 'delete-history-item':
      return await deleteHistoryItem(body);
    
    default:
      return createErrorResponse({
        code: 'INVALID_ACTION',
        message: `Unsupported action: ${action}. Supported actions: delete-history-item`,
        timestamp: new Date().toISOString(),
      }, 400);
  }
});

/**
 * Get user usage statistics
 */
async function getUserUsage(params: Record<string, string>) {
  const validation = getUserUsageSchema.safeParse({
    startUnix: params.start_unix ? parseInt(params.start_unix) : undefined,
    endUnix: params.end_unix ? parseInt(params.end_unix) : undefined,
  });

  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid parameters',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  try {
    let endpoint = '/user/subscription';
    const queryParams = new URLSearchParams();

    if (validation.data.startUnix !== undefined) {
      queryParams.append('start_unix', validation.data.startUnix.toString());
    }
    if (validation.data.endUnix !== undefined) {
      queryParams.append('end_unix', validation.data.endUnix.toString());
    }

    if (queryParams.toString()) {
      endpoint += `?${queryParams.toString()}`;
    }

    const response = await makeElevenLabsRequest(endpoint);
    const usage = await response.json();

    return createSuccessResponse(usage);
  } catch (error) {
    return createErrorResponse({
      code: 'ELEVENLABS_ERROR',
      message: error instanceof Error ? error.message : 'Failed to fetch user usage',
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Get user history
 */
async function getHistory(params: Record<string, string>) {
  try {
    let endpoint = '/history';
    const queryParams = new URLSearchParams();

    if (params.page_size) {
      queryParams.append('page_size', params.page_size);
    }
    if (params.start_after_history_item_id) {
      queryParams.append('start_after_history_item_id', params.start_after_history_item_id);
    }

    if (queryParams.toString()) {
      endpoint += `?${queryParams.toString()}`;
    }

    const response = await makeElevenLabsRequest(endpoint);
    const history = await response.json();

    return createSuccessResponse(history);
  } catch (error) {
    return createErrorResponse({
      code: 'ELEVENLABS_ERROR',
      message: error instanceof Error ? error.message : 'Failed to fetch history',
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Download history item audio
 */
async function downloadHistoryItem(params: Record<string, string>) {
  const validation = downloadHistoryItemSchema.safeParse(params);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid parameters',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  try {
    const response = await makeElevenLabsRequest(`/history/${validation.data.history_item_id}/audio`);
    const audioBuffer = await response.arrayBuffer();

    return new Response(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Disposition': `attachment; filename="history-${validation.data.history_item_id}.mp3"`,
        'Content-Length': audioBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    return createErrorResponse({
      code: 'ELEVENLABS_ERROR',
      message: error instanceof Error ? error.message : 'Failed to download history item',
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * Delete history item
 */
async function deleteHistoryItem(body: unknown) {
  const validation = deleteHistoryItemSchema.safeParse(body);
  if (!validation.success) {
    return createErrorResponse({
      code: 'VALIDATION_ERROR',
      message: 'Invalid request body',
      details: validation.error.errors,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  try {
    await makeElevenLabsRequest(`/history/${validation.data.history_item_id}`, {
      method: 'DELETE',
    });

    return createSuccessResponse({ success: true });
  } catch (error) {
    return createErrorResponse({
      code: 'ELEVENLABS_ERROR',
      message: error instanceof Error ? error.message : 'Failed to delete history item',
      timestamp: new Date().toISOString(),
    }, 500);
  }
}

/**
 * OPTIONS /api/elevenlabs
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
