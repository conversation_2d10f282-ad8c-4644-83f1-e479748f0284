# Sentry Integration Setup Guide

This guide covers the complete setup and usage of the Sentry error monitoring and performance tracking integration in your Next.js 14 App Router application.

## Overview

Sentry is a comprehensive application monitoring platform that provides error tracking, performance monitoring, and session replay. This integration provides:

- **Error Monitoring** - Automatic error capture and reporting
- **Performance Tracking** - Monitor API calls, page loads, and user interactions
- **Session Replay** - Video-like reproduction of user sessions
- **User Context** - Track user information and custom metadata
- **Source Maps** - Readable stack traces in production
- **Real-time Alerts** - Get notified when issues occur

## Prerequisites

1. **Sentry Account**: Sign up at [sentry.io](https://sentry.io)
2. **Sentry Project**: Create a new project for your application
3. **DSN**: Get your Data Source Name from the project settings
4. **Auth Token**: Generate an auth token for source map uploads (optional)

## Installation

The Sentry dependencies are already included in the project:

```bash
pnpm install
```

## Environment Configuration

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Configure your Sentry credentials in `.env.local`:

```env
# Sentry Configuration (Error Monitoring & Performance)
# Get your DSN from: https://sentry.io/settings/[org]/projects/[project]/keys/

# Sentry DSN (Data Source Name) - PUBLIC
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/0

# Sentry Organization and Project (for source maps and CLI)
SENTRY_ORG=your_sentry_org_here
SENTRY_PROJECT=your_sentry_project_here

# Sentry Auth Token (for uploading source maps) - KEEP SECRET
SENTRY_AUTH_TOKEN=sntrys_your_auth_token_here

# Sentry Environment (development, staging, production)
SENTRY_ENVIRONMENT=development

# Sentry Configuration Options
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_REPLAYS_SESSION_SAMPLE_RATE=0.1
SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE=1.0
SENTRY_SEND_DEFAULT_PII=false
SENTRY_DEBUG=false

# Feature Flag
FEATURE_SENTRY=true
```

### Environment Variables Explained

- **`NEXT_PUBLIC_SENTRY_DSN`**: Public DSN for client-side error reporting
- **`SENTRY_ORG`**: Your Sentry organization slug
- **`SENTRY_PROJECT`**: Your Sentry project slug
- **`SENTRY_AUTH_TOKEN`**: Auth token for uploading source maps (optional)
- **`SENTRY_ENVIRONMENT`**: Environment name (auto-detected from NODE_ENV if not set)
- **`SENTRY_TRACES_SAMPLE_RATE`**: Percentage of transactions to capture (0.0 to 1.0)
- **`SENTRY_REPLAYS_SESSION_SAMPLE_RATE`**: Percentage of sessions to record
- **`SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE`**: Percentage of error sessions to record
- **`SENTRY_SEND_DEFAULT_PII`**: Whether to include user IP, cookies, etc.
- **`SENTRY_DEBUG`**: Enable debug logging

## Configuration Files

The integration includes several configuration files:

### 1. `sentry.server.config.ts`
Server-side Sentry configuration for Node.js runtime.

### 2. `sentry.edge.config.ts`
Edge runtime Sentry configuration for Edge API routes.

### 3. `instrumentation-client.ts`
Client-side Sentry configuration with session replay and performance monitoring.

### 4. `instrumentation.ts`
Next.js instrumentation file that registers Sentry configurations.

### 5. `app/global-error.tsx`
Global error boundary that captures React render errors.

## Client-Side Usage

### Basic Hook Usage

```typescript
import { useSentry } from '@/hooks/useSentry';

function MyComponent() {
  const {
    captureError,
    captureMessage,
    setUser,
    addBreadcrumb,
    isEnabled,
  } = useSentry({
    enablePerformanceMonitoring: true,
    enableSessionReplay: true,
    defaultTags: {
      component: 'MyComponent',
    },
  });

  const handleError = () => {
    try {
      // Some operation that might fail
      throw new Error('Something went wrong');
    } catch (error) {
      captureError(error as Error, {
        tags: {
          action: 'button-click',
        },
        extra: {
          timestamp: Date.now(),
        },
      });
    }
  };

  const handleLogin = (user: any) => {
    setUser({
      id: user.id,
      email: user.email,
      username: user.username,
    });
    
    addBreadcrumb({
      category: 'auth',
      message: 'User logged in',
      level: 'info',
    });
  };

  return (
    <div>
      <button onClick={handleError}>Test Error</button>
      <button onClick={() => handleLogin({ id: '123', email: '<EMAIL>' })}>
        Login
      </button>
    </div>
  );
}
```

### API Error Handling

```typescript
import { useSentryAPIError } from '@/hooks/useSentry';

function useAPI() {
  const captureAPIError = useSentryAPIError();

  const fetchData = async () => {
    try {
      const response = await fetch('/api/data');
      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      captureAPIError(
        error as Error,
        '/api/data',
        'GET',
        500,
        'req-123',
        1500 // duration in ms
      );
      throw error;
    }
  };

  return { fetchData };
}
```

### Performance Monitoring

```typescript
import { useSentryPerformance } from '@/hooks/useSentry';

function usePerformanceTracking() {
  const measurePerformance = useSentryPerformance();

  const expensiveOperation = async () => {
    return await measurePerformance(
      'function',
      'Expensive Operation',
      async () => {
        // Your expensive operation here
        await new Promise(resolve => setTimeout(resolve, 1000));
        return 'result';
      },
      {
        category: 'computation',
        complexity: 'high',
      }
    );
  };

  return { expensiveOperation };
}
```

## Server-Side Usage

### API Route Error Handling

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { captureAPIError } from '@/lib/sentry';

export async function GET(request: NextRequest) {
  try {
    // Your API logic here
    const data = await fetchSomeData();
    return NextResponse.json(data);
  } catch (error) {
    const eventId = captureAPIError(error as Error, {
      endpoint: '/api/example',
      method: 'GET',
      statusCode: 500,
      requestId: 'req-123',
    });

    return NextResponse.json(
      { error: 'Internal Server Error', eventId },
      { status: 500 }
    );
  }
}
```

### Manual Error Capture

```typescript
import { captureError, captureMessage } from '@/lib/sentry';

// Capture an error with context
const eventId = captureError(new Error('Something went wrong'), {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
  },
  tags: {
    feature: 'payment',
    action: 'process',
  },
  extra: {
    paymentId: 'pay-123',
    amount: 99.99,
  },
});

// Capture a message
captureMessage('Payment processed successfully', 'info', {
  tags: {
    feature: 'payment',
  },
  extra: {
    paymentId: 'pay-123',
  },
});
```

## Testing Your Setup

### 1. Test Error Capture

Create a test page or add to an existing component:

```typescript
'use client';

import { useSentry } from '@/hooks/useSentry';

export default function SentryTestPage() {
  const { captureError, captureMessage } = useSentry();

  const testError = () => {
    captureError(new Error('Test error from client'), {
      tags: { test: 'true' },
    });
  };

  const testMessage = () => {
    captureMessage('Test message from client', 'info', {
      tags: { test: 'true' },
    });
  };

  const testUnhandledError = () => {
    throw new Error('Unhandled test error');
  };

  return (
    <div className="p-4 space-y-4">
      <h1>Sentry Test Page</h1>
      <button onClick={testError} className="bg-red-500 text-white p-2 rounded">
        Test Captured Error
      </button>
      <button onClick={testMessage} className="bg-blue-500 text-white p-2 rounded">
        Test Message
      </button>
      <button onClick={testUnhandledError} className="bg-orange-500 text-white p-2 rounded">
        Test Unhandled Error
      </button>
    </div>
  );
}
```

### 2. Test API Endpoint

Visit `/api/sentry?action=test` to test the server-side integration.

### 3. Check Sentry Dashboard

1. Go to your Sentry project dashboard
2. Check the **Issues** tab for captured errors
3. Check the **Performance** tab for transaction data
4. Check the **Replays** tab for session recordings

## Features

### Error Monitoring
- Automatic error capture
- Custom error reporting
- Error grouping and deduplication
- Stack traces with source maps
- Error context and metadata

### Performance Monitoring
- Automatic transaction tracking
- Custom span creation
- API call monitoring
- Page load performance
- User interaction tracking

### Session Replay
- Video-like session recordings
- Error session capture
- User interaction replay
- Privacy controls (masking)

### User Context
- User identification
- Custom user metadata
- Subscription information
- Authentication state

### Breadcrumbs
- Navigation tracking
- API call logging
- User action recording
- Custom event tracking

## Best Practices

1. **Environment Configuration**: Use different sample rates for different environments
2. **Error Filtering**: Filter out non-actionable errors
3. **User Privacy**: Be careful with PII in error reports
4. **Performance Impact**: Adjust sample rates based on traffic
5. **Source Maps**: Upload source maps for readable stack traces
6. **Custom Context**: Add relevant context to errors
7. **User Feedback**: Enable user feedback for better debugging

## Troubleshooting

### Common Issues

1. **No events in Sentry**: Check DSN configuration and network connectivity
2. **Source maps not working**: Verify auth token and build configuration
3. **Too many events**: Adjust sample rates and error filtering
4. **Performance impact**: Lower sample rates or disable features

### Debug Mode

Enable debug mode in development:

```env
SENTRY_DEBUG=true
```

This will log Sentry operations to the console.

## Next Steps

1. **Set up Alerts**: Configure alerts for critical errors
2. **Create Dashboards**: Build custom dashboards for monitoring
3. **Integrate with CI/CD**: Automate release tracking
4. **Team Notifications**: Set up team notifications for issues
5. **Performance Budgets**: Set performance thresholds

## Additional Configuration

### Source Maps Upload

To enable readable stack traces in production, configure source map uploads:

1. **Generate Auth Token**: Go to Sentry → Settings → Auth Tokens
2. **Set Permissions**: Grant `project:releases` and `org:read` permissions
3. **Add to Environment**: Set `SENTRY_AUTH_TOKEN` in your environment
4. **Build Configuration**: Source maps are automatically uploaded during build

### Tunneling (Optional)

To avoid ad blockers, enable tunneling in `next.config.ts`:

```typescript
tunnelRoute: "/monitoring"
```

This routes Sentry requests through your domain instead of directly to Sentry.

### Release Tracking

Automatically track releases by setting:

```env
SENTRY_RELEASE=your-app@1.0.0
```

Or let Sentry auto-generate from your package.json version.

## Monitoring and Alerts

### Set Up Alerts

1. Go to **Alerts** in your Sentry project
2. Create alert rules for:
   - New issues
   - High error rates
   - Performance degradation
   - Custom metrics

### Dashboard Creation

1. Go to **Dashboards** in Sentry
2. Create custom dashboards with:
   - Error rate trends
   - Performance metrics
   - User impact analysis
   - Release comparisons

## Integration with Other Services

### RevenueCat Integration

```typescript
import { useSentry } from '@/hooks/useSentry';
import { useRevenueCat } from '@/hooks/useRevenueCat';

function SubscriptionComponent() {
  const { setUser, captureError } = useSentry();
  const { customerInfo, error } = useRevenueCat();

  useEffect(() => {
    if (customerInfo) {
      setUser({
        id: customerInfo.originalAppUserId,
        subscription: {
          plan: customerInfo.activeSubscriptions[0] || 'free',
          status: customerInfo.entitlements.active ? 'active' : 'inactive',
        },
      });
    }

    if (error) {
      captureError(error, {
        tags: { service: 'revenuecat' },
      });
    }
  }, [customerInfo, error, setUser, captureError]);
}
```

### ElevenLabs Integration

```typescript
import { useSentry } from '@/hooks/useSentry';
import { useElevenLabs } from '@/hooks/useElevenLabs';

function TextToSpeechComponent() {
  const { captureError, startSpan } = useSentry();
  const { generateSpeech } = useElevenLabs();

  const handleGenerate = async (text: string) => {
    try {
      await startSpan({
        op: 'ai.generation',
        description: 'ElevenLabs Text-to-Speech',
        tags: { service: 'elevenlabs' },
      });

      await generateSpeech(text);
    } catch (error) {
      captureError(error as Error, {
        tags: { service: 'elevenlabs' },
        extra: { text },
      });
    }
  };
}
```

The Sentry integration is now complete and ready for production use with comprehensive error monitoring, performance tracking, and session replay capabilities.
