# Dappier Integration Setup Guide

This guide covers the complete setup and usage of the Dappier AI search and custom copilots integration in your Next.js 14 App Router application.

## Overview

Dappier is an AI-powered platform that provides real-time data access, intelligent search, and custom copilots. This integration provides:

- **Real-Time Data API** - Access live data from trusted sources
- **AI-Powered Search** - Semantic search with natural language queries
- **Custom Copilots** - Intelligent chatbots with context awareness
- **AI Recommendations** - Personalized content and product suggestions
- **RAG Marketplace** - Pre-trained models for various verticals

## Prerequisites

1. **Dappier Account**: Sign up at [dappier.com](https://dappier.com)
2. **API Key**: Get your API key from the Dappier platform
3. **Model Access**: Choose appropriate models from the RAG marketplace

## Installation

The Dappier integration is already included in the project. No additional packages are required.

## Environment Configuration

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Configure your Dappier credentials in `.env.local`:

```env
# Dappier Configuration (AI Search & Custom Copilots)
# Get your API key from: https://platform.dappier.com/settings/profile

# Dappier API Key (for server-side operations) - KEEP SECRET
DAPPIER_API_KEY=your_dappier_api_key_here

# Dappier Base URL (API endpoint)
DAPPIER_BASE_URL=https://api.dappier.com

# Dappier Configuration Options
DAPPIER_DEFAULT_MODEL=am_01j06ytn18ejftedz6dyhz2b15
DAPPIER_TIMEOUT=30000
DAPPIER_MAX_RETRIES=3
DAPPIER_ENABLE_CACHING=true
DAPPIER_CACHE_TTL=300

# Feature Flag
FEATURE_DAPPIER=true
```

### Environment Variables Explained

- **`DAPPIER_API_KEY`**: Your Dappier API key for authentication
- **`DAPPIER_BASE_URL`**: Dappier API endpoint (default: https://api.dappier.com)
- **`DAPPIER_DEFAULT_MODEL`**: Default AI model ID to use
- **`DAPPIER_TIMEOUT`**: Request timeout in milliseconds
- **`DAPPIER_MAX_RETRIES`**: Maximum number of retry attempts
- **`DAPPIER_ENABLE_CACHING`**: Enable response caching
- **`DAPPIER_CACHE_TTL`**: Cache time-to-live in seconds

## Getting Your API Key

1. Visit [platform.dappier.com](https://platform.dappier.com)
2. Sign up or log in to your account
3. Navigate to Settings → Profile
4. Generate or copy your API key
5. Add it to your `.env.local` file

## Available Models

Dappier provides various pre-trained models in their RAG marketplace:

- **News & Current Events**: Real-time news and updates
- **Financial Data**: Market data, stock prices, economic indicators
- **Sports**: Live scores, statistics, player information
- **Weather**: Current conditions and forecasts
- **General Knowledge**: Broad knowledge base for Q&A

Visit the [RAG Marketplace](https://marketplace.dappier.com) to explore available models.

## Client-Side Usage

### Basic Hook Usage

```typescript
import { useDappier } from '@/hooks/useDappier';

function MyComponent() {
  const {
    queryRealTimeData,
    search,
    getRecommendations,
    sendMessage,
    isLoading,
    error,
  } = useDappier({
    enableCaching: true,
    autoRetry: true,
  });

  const handleQuery = async () => {
    const response = await queryRealTimeData({
      query: 'What is the current weather in New York?',
      includeMetadata: true,
    });
    
    console.log('Response:', response);
  };

  return (
    <div>
      <button onClick={handleQuery} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Get Weather'}
      </button>
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

### Real-Time Data Queries

```typescript
import { useDappierQuery } from '@/hooks/useDappier';

function RealTimeDataComponent() {
  const { query, isLoading, error } = useDappierQuery();

  const handleQuery = async () => {
    const response = await query('Latest news about AI technology', {
      maxResults: 5,
      includeMetadata: true,
    });
    
    console.log('News:', response);
  };

  return (
    <div>
      <button onClick={handleQuery}>Get Latest AI News</button>
      {isLoading && <p>Loading...</p>}
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

### AI Search

```typescript
import { useDappierSearch } from '@/hooks/useDappier';

function SearchComponent() {
  const { search, isLoading, error } = useDappierSearch();

  const handleSearch = async (query: string) => {
    const results = await search(query, {
      type: 'semantic',
      pagination: { page: 1, limit: 10 },
      includeSnippets: true,
    });
    
    console.log('Search results:', results);
  };

  return (
    <div>
      <input 
        type="text" 
        placeholder="Search..."
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleSearch(e.currentTarget.value);
          }
        }}
      />
      {isLoading && <p>Searching...</p>}
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

### AI Recommendations

```typescript
import { useDappierRecommendations } from '@/hooks/useDappier';

function RecommendationsComponent() {
  const { recommend, isLoading, error } = useDappierRecommendations();

  const getPersonalizedRecommendations = async () => {
    const recommendations = await recommend('user-123', {
      type: 'content',
      maxResults: 5,
      context: {
        currentPage: '/dashboard',
        preferences: {
          topics: ['technology', 'ai', 'programming'],
        },
      },
    });
    
    console.log('Recommendations:', recommendations);
  };

  return (
    <div>
      <button onClick={getPersonalizedRecommendations}>
        Get Recommendations
      </button>
      {isLoading && <p>Loading recommendations...</p>}
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

### Custom Copilot

```typescript
import { useDappierCopilot } from '@/hooks/useDappier';

function CopilotComponent() {
  const { 
    chat, 
    messages, 
    conversationId, 
    clearConversation,
    isLoading, 
    error 
  } = useDappierCopilot();

  const sendMessage = async (message: string) => {
    const response = await chat(message, {
      personality: {
        tone: 'friendly',
        responseStyle: 'conversational',
      },
      capabilities: [
        { type: 'search', name: 'Web Search', enabled: true },
        { type: 'recommendation', name: 'Content Recommendations', enabled: true },
      ],
    });
    
    console.log('Copilot response:', response);
  };

  return (
    <div>
      <div className="messages">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.role}`}>
            <strong>{msg.role}:</strong> {msg.content}
          </div>
        ))}
      </div>
      
      <input 
        type="text" 
        placeholder="Ask me anything..."
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            sendMessage(e.currentTarget.value);
            e.currentTarget.value = '';
          }
        }}
      />
      
      <button onClick={clearConversation}>Clear Conversation</button>
      {isLoading && <p>Thinking...</p>}
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

## UI Components

### Search Bar Component

```typescript
import { SearchBar } from '@/components/dappier/search-bar';

function MyPage() {
  return (
    <SearchBar
      placeholder="Search with AI..."
      showSuggestions={true}
      maxResults={10}
      onResultSelect={(result) => {
        console.log('Selected:', result);
      }}
      onSearch={(query, results) => {
        console.log('Search:', query, results);
      }}
    />
  );
}
```

### Copilot Chat Component

```typescript
import { CopilotChat } from '@/components/dappier/copilot-chat';

function MyPage() {
  return (
    <CopilotChat
      title="AI Assistant"
      personality={{
        tone: 'friendly',
        responseStyle: 'conversational',
      }}
      capabilities={[
        { type: 'search', name: 'Web Search', enabled: true },
        { type: 'recommendation', name: 'Recommendations', enabled: true },
      ]}
      maxHeight="500px"
      onMessageSent={(message) => {
        console.log('User sent:', message);
      }}
      onResponseReceived={(response) => {
        console.log('AI responded:', response);
      }}
    />
  );
}
```

### Recommendations Component

```typescript
import { Recommendations } from '@/components/dappier/recommendations';

function MyPage() {
  return (
    <Recommendations
      userId="user-123"
      type="content"
      maxResults={5}
      showReasons={true}
      autoRefresh={true}
      onRecommendationClick={(recommendation) => {
        console.log('Clicked:', recommendation);
      }}
      onRecommendationFeedback={(id, feedback) => {
        console.log('Feedback:', id, feedback);
      }}
    />
  );
}
```

## Server-Side Usage

### API Route Usage

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { queryRealTimeData } from '@/lib/dappier';

export async function POST(request: NextRequest) {
  const { query } = await request.json();
  
  const response = await queryRealTimeData({
    query,
    includeMetadata: true,
  });
  
  if (response.success) {
    return NextResponse.json(response.data);
  } else {
    return NextResponse.json(
      { error: response.error },
      { status: 500 }
    );
  }
}
```

### Direct API Usage

```typescript
import { 
  queryRealTimeData, 
  performSearch, 
  getRecommendations,
  sendCopilotMessage 
} from '@/lib/dappier';

// Real-time data query
const newsResponse = await queryRealTimeData({
  query: 'Latest technology news',
  maxResults: 5,
});

// Search
const searchResponse = await performSearch({
  query: 'artificial intelligence',
  type: 'semantic',
  pagination: { page: 1, limit: 10 },
});

// Recommendations
const recommendationsResponse = await getRecommendations({
  userId: 'user-123',
  type: 'content',
  maxResults: 5,
});

// Copilot message
const copilotResponse = await sendCopilotMessage({
  message: 'Hello, how can you help me?',
  personality: {
    tone: 'professional',
    responseStyle: 'concise',
  },
});
```

## Testing Your Setup

### 1. Test API Connection

Visit `/api/dappier?action=test` to test the server-side integration.

### 2. Test Components

Create a test page with the Dappier components:

```typescript
'use client';

import { SearchBar } from '@/components/dappier/search-bar';
import { CopilotChat } from '@/components/dappier/copilot-chat';
import { Recommendations } from '@/components/dappier/recommendations';

export default function DappierTestPage() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1>Dappier Integration Test</h1>
      
      <section>
        <h2>AI Search</h2>
        <SearchBar
          placeholder="Search with AI..."
          showSuggestions={true}
          onResultSelect={(result) => console.log('Selected:', result)}
        />
      </section>
      
      <section>
        <h2>AI Assistant</h2>
        <CopilotChat
          title="AI Assistant"
          maxHeight="400px"
        />
      </section>
      
      <section>
        <h2>AI Recommendations</h2>
        <Recommendations
          type="content"
          maxResults={5}
          showReasons={true}
        />
      </section>
    </div>
  );
}
```

## Features

### Real-Time Data Access
- Live news and updates
- Financial market data
- Sports scores and statistics
- Weather information
- Custom data sources

### AI-Powered Search
- Semantic search capabilities
- Natural language queries
- Contextual results
- Search suggestions
- Result ranking and scoring

### Custom Copilots
- Conversational AI assistants
- Context-aware responses
- Customizable personalities
- Multi-turn conversations
- Action suggestions

### AI Recommendations
- Personalized content suggestions
- User behavior analysis
- Context-aware recommendations
- Feedback collection
- A/B testing support

## Best Practices

1. **API Key Security**: Keep your API key secure and never expose it client-side
2. **Caching**: Enable caching for frequently accessed data
3. **Error Handling**: Implement robust error handling for API failures
4. **Rate Limiting**: Be mindful of API rate limits and usage quotas
5. **User Privacy**: Be transparent about data usage in AI features
6. **Performance**: Use debouncing for search inputs to reduce API calls
7. **Fallbacks**: Provide fallback content when AI services are unavailable

## Troubleshooting

### Common Issues

1. **No API response**: Check API key configuration and network connectivity
2. **Rate limiting**: Implement proper caching and request throttling
3. **Model not found**: Verify model ID in the RAG marketplace
4. **Timeout errors**: Increase timeout values for complex queries

### Debug Mode

Enable debug logging by setting:

```env
NODE_ENV=development
```

This will log API requests and responses to the console.

## Integration with Other Services

### RevenueCat Integration

```typescript
import { useDappier } from '@/hooks/useDappier';
import { useRevenueCat } from '@/hooks/useRevenueCat';

function PersonalizedComponent() {
  const { getRecommendations } = useDappier();
  const { customerInfo } = useRevenueCat();

  const getPersonalizedContent = async () => {
    const recommendations = await getRecommendations({
      userId: customerInfo?.originalAppUserId,
      context: {
        userProfile: {
          subscription: {
            plan: customerInfo?.activeSubscriptions[0] || 'free',
            status: customerInfo?.entitlements.active ? 'active' : 'inactive',
          },
        },
      },
    });
    
    return recommendations;
  };
}
```

### ElevenLabs Integration

```typescript
import { useDappier } from '@/hooks/useDappier';
import { useElevenLabs } from '@/hooks/useElevenLabs';

function VoiceAssistant() {
  const { sendMessage } = useDappier();
  const { generateSpeech } = useElevenLabs();

  const handleVoiceQuery = async (query: string) => {
    // Get AI response
    const response = await sendMessage({
      message: query,
      personality: { tone: 'friendly', responseStyle: 'conversational' },
    });
    
    // Convert to speech
    await generateSpeech(response.message);
  };
}
```

The Dappier integration is now complete and ready for production use with comprehensive AI search, custom copilots, and intelligent recommendations.
